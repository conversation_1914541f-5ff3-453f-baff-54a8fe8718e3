{"timestamp": "2025-07-03T16:58:22.440863", "overall_status": "❌ NEEDS CONFIGURATION", "components": {"environment": {"status": "success", "details": ["✅ DATABASE_URL configured", "✅ GITHUB_TOKEN configured"]}, "database": {"status": "error", "details": ["❌ Database error: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."]}, "github": {"status": "success", "details": ["✅ GitHub authentication successful for user: yerenwgventures", "✅ Repository access confirmed"]}, "sync_manager": {"status": "error", "details": ["✅ Sync manager configuration loaded", "✅ Required directories verified", "❌ Sync manager error: 'auto_sync'"]}, "scheduler": {"status": "error", "details": ["❌ Scheduler error: 'auto_sync'"]}, "automation_system": {"status": "success", "details": ["✅ Automation system configuration loaded", "✅ Status report generation working"]}, "directories": {"status": "success", "details": ["✅ uploads/ exists", "✅ automation_logs/ exists", "✅ automation_modules/ exists", "✅ static/ exists", "✅ templates/ exists", "✅ config/ exists"]}, "web_application": {"status": "success", "details": ["✅ Flask application configured", "✅ Automation blueprint registered", "✅ 10 templates found"]}}, "recommendations": ["🗄️ Verify database configuration and connectivity"], "critical_issues": ["Database connection failed: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information.", "Module sync manager failed: 'auto_sync'", "Hourly scheduler failed: 'auto_sync'"], "warnings": []}