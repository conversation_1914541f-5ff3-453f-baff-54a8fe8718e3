from odoo import models, fields, api

class TestModel(models.Model):
    _name = 'test.model'
    _inherit = 'mail.thread'
    
    name = fields.Char('Name')
    amount = fields.Float('Amount')
    
    @api.one
    def compute_total(self):
        # This method uses dangerous @api.one decorator
        self.total = self.amount * 2
        self.write({'total': self.total})
    
    @api.multi
    def old_method(self):
        # This uses obsolete @api.multi
        return self.search([])
