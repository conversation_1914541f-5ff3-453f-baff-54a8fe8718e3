{% extends "base.html" %}

{% block title %}Automation Dashboard{% endblock %}

{% block content %}
<style>
.module-count-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
.module-count-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}
.module-count-available {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}
.module-count-empty {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
</style>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-robot me-2"></i>Automation Dashboard</h2>
                <div class="btn-group" role="group">
                    {% if not status.system_initialized %}
                        <form method="post" action="{{ url_for('automation.initialize_automation') }}" class="d-inline">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-play me-1"></i>Initialize System
                            </button>
                        </form>
                    {% else %}
                        <form method="post" action="{{ url_for('automation.run_automation_cycle') }}" class="d-inline">
                            <button type="submit" class="btn btn-success" {{ 'disabled' if status.is_running else '' }}>
                                <i class="fas fa-sync {{ 'fa-spin' if status.is_running else '' }} me-1"></i>
                                {{ 'Running...' if status.is_running else 'Run Cycle' }}
                            </button>
                        </form>
                        <form method="post" action="{{ url_for('automation.sync_modules') }}" class="d-inline ms-2">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-download me-1"></i>Sync Modules
                            </button>
                        </form>
                    {% endif %}
                    <a href="{{ url_for('automation.automation_config') }}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-cog me-1"></i>Configure
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card {{ 'border-success' if status.system_initialized else 'border-warning' }}">
                <div class="card-body text-center">
                    <i class="fas fa-{{ 'check-circle text-success' if status.system_initialized else 'exclamation-triangle text-warning' }} fa-2x mb-2"></i>
                    <h5 class="card-title">System Status</h5>
                    <p class="card-text">{{ 'Initialized' if status.system_initialized else 'Not Initialized' }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card {{ 'border-info' if status.is_running else 'border-secondary' }}">
                <div class="card-body text-center">
                    <i class="fas fa-{{ 'spinner fa-spin text-info' if status.is_running else 'pause text-secondary' }} fa-2x mb-2"></i>
                    <h5 class="card-title">Current Status</h5>
                    <p class="card-text">{{ 'Running' if status.is_running else 'Idle' }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-clock text-primary fa-2x mb-2"></i>
                    <h5 class="card-title">Last Run</h5>
                    <p class="card-text">
                        {% if status.last_run %}
                            {{ moment(status.last_run).format('MMM DD, HH:mm') }}
                        {% else %}
                            Never
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line text-info fa-2x mb-2"></i>
                    <h5 class="card-title">Success Rate</h5>
                    <p class="card-text">
                        {% if status.stats.total_processed > 0 %}
                            {{ ((status.stats.successful_upgrades / status.stats.total_processed) * 100) | round(1) }}%
                        {% else %}
                            N/A
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    {% if status.stats.total_processed > 0 %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Processing Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="display-6 text-primary">{{ status.stats.total_processed }}</div>
                                <small class="text-muted">Total Processed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="display-6 text-success">{{ status.stats.successful_upgrades }}</div>
                                <small class="text-muted">Successful</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="display-6 text-danger">{{ status.stats.failed_upgrades }}</div>
                                <small class="text-muted">Failed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="display-6 text-info">{{ status.stats.last_cycle_duration | round(0) }}s</div>
                                <small class="text-muted">Last Cycle Duration</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Module Status Report -->
    {% if report %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                    <h5 class="mb-1 text-primary">
                        <i class="fas fa-chart-bar me-2"></i>Module Distribution by Odoo Version
                    </h5>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Shows how many modules are available for each Odoo version
                    </small>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for version, count in report.modules_by_version.items() %}
                        <div class="col-md-2 mb-3">
                            {% if count > 0 %}
                            <div class="module-count-card module-count-available text-center p-4 text-white border rounded shadow-sm">
                                <div class="h2 fw-bold text-white mb-1">{{ count }}</div>
                                <div class="text-white-75 mb-2">Odoo {{ version.upper() }}</div>
                                <span class="badge bg-success bg-gradient">
                                    <i class="fas fa-check me-1"></i>Available
                                </span>
                            </div>
                            {% else %}
                            <div class="module-count-card module-count-empty text-center p-4 border border-2 border-secondary rounded shadow-sm">
                                <div class="h2 fw-bold text-secondary mb-1">{{ count }}</div>
                                <div class="text-muted mb-2">Odoo {{ version.upper() }}</div>
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times me-1"></i>None
                                </span>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>

                    <div class="mt-4">
                        <div class="alert alert-info border-0 bg-light">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2 text-info"></i>Understanding Module Distribution
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <strong>Available Modules</strong>
                                    </div>
                                    <small class="text-muted">Modules ready for use in each Odoo version</small>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-arrow-up text-primary me-2"></i>
                                        <strong>Migration Process</strong>
                                    </div>
                                    <small class="text-muted">Automatic upgrades from older to newer versions</small>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-robot text-warning me-2"></i>
                                        <strong>TrueMigrationOrchestrator</strong>
                                    </div>
                                    <small class="text-muted">Handles the entire migration workflow</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% if report.pending_upgrades > 0 %}
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>{{ report.pending_upgrades }}</strong> modules are pending upgrade to newer versions.
                    </div>
                    {% else %}
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle me-2"></i>
                        All modules are up to date! No pending upgrades found.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Version Chain -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-arrow-right me-2"></i>Upgrade Path</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center">
                        {% if report and report.version_chain %}
                            {% for version in report.version_chain %}
                                <div class="badge bg-primary fs-6 px-3 py-2">
                                    Odoo {{ version }}
                                </div>
                                {% if not loop.last %}
                                    <i class="fas fa-arrow-right mx-3 text-muted"></i>
                                {% endif %}
                            {% endfor %}
                        {% else %}
                            <span class="text-muted">15.0 → 16.0 → 17.0 → 18.0</span>
                        {% endif %}
                    </div>
                    <p class="text-center text-muted mt-2 mb-0">
                        Modules are automatically upgraded through each version step
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-lightning-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('automation.automation_logs') }}" class="btn btn-outline-primary">
                            <i class="fas fa-file-alt me-2"></i>View Logs
                        </a>
                        <a href="{{ url_for('main.analyze_modules') }}" class="btn btn-outline-info">
                            <i class="fas fa-search me-2"></i>Module Analysis
                        </a>
                        <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-outline-success">
                            <i class="fas fa-upload me-2"></i>Upload Modules
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>System Information</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-6">System Status:</dt>
                        <dd class="col-sm-6">
                            <span class="badge bg-{{ 'success' if status.system_initialized else 'warning' }}">
                                {{ 'Ready' if status.system_initialized else 'Setup Required' }}
                            </span>
                        </dd>
                        
                        <dt class="col-sm-6">Processing Mode:</dt>
                        <dd class="col-sm-6">
                            <span class="badge bg-info">Batch Processing</span>
                        </dd>
                        
                        {% if report and report.processing_config %}
                        <dt class="col-sm-6">Batch Size:</dt>
                        <dd class="col-sm-6">{{ report.processing_config.batch_size }} modules</dd>
                        
                        <dt class="col-sm-6">Quality Threshold:</dt>
                        <dd class="col-sm-6">{{ report.processing_config.quality_threshold }}%</dd>
                        {% endif %}
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh status every 30 seconds when automation is running
{% if status.is_running %}
setTimeout(function() {
    location.reload();
}, 30000);
{% endif %}

// Real-time status updates via AJAX
function updateStatus() {
    fetch('/automation/api/status')
        .then(response => response.json())
        .then(data => {
            if (data.is_running !== {{ status.is_running | tojson }}) {
                location.reload();
            }
        })
        .catch(console.error);
}

// Update every 10 seconds
setInterval(updateStatus, 10000);
</script>
{% endblock %}