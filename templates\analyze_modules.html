{% extends "base.html" %}
{% set title = "Analyze Modules" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-search me-3"></i>
            Module Analysis
        </h1>
        <p class="lead">Review uploaded modules and their compatibility analysis</p>
    </div>
</div>

<!-- Action Bar -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">
                            <i class="fas fa-cubes me-2"></i>
                            {{ modules|length }} Modules Uploaded
                        </h6>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ url_for('main.upload_modules') }}" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>Upload More
                        </a>
                        <a href="{{ url_for('main.analyze_all') }}" class="btn btn-success">
                            <i class="fas fa-cogs me-1"></i>Analyze All Pending
                        </a>
                        <button class="btn btn-outline-secondary" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modules List -->
{% if modules %}
<!-- Workflow Guidance for All Modules -->
<div class="row mb-4">
    <div class="col">
        <div class="alert alert-info" role="alert">
            <h6 class="alert-heading"><i class="fas fa-info-circle me-1"></i>Migration Workflow Guide</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-warning text-dark me-2">1</span>
                        <strong>Auto-Fix first</strong> - Quick cleanup (imports, sudo, encoding)
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary me-2">2</span>
                        <strong>True Migration second</strong> - Complete version upgrade + AI analysis
                    </div>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        <strong>Best Practice:</strong> Auto-Fix prepares modules for smoother migration by handling basic compatibility issues first.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Module</th>
                                <th>Upload Date</th>
                                <th>File Size</th>
                                <th>Analysis Status</th>
                                <th>Compatibility</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in modules_with_analysis %}
                            {% set module = item.module %}
                            {% set latest_job = item.latest_job %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-cube me-2 text-primary"></i>
                                        <div>
                                            <strong>{{ module.name }}</strong>
                                            <small class="text-muted d-block">{{ module.filename }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <small>{{ module.upload_date.strftime('%Y-%m-%d %H:%M') if module.upload_date else 'Unknown' }}</small>
                                </td>
                                <td>
                                    {% if module.file_size %}
                                        {{ (module.file_size / 1024 / 1024) | round(2) }} MB
                                    {% else %}
                                        Unknown
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.has_analysis %}
                                        <span class="badge bg-{{ 'success' if item.analysis_status == 'COMPLETED' else 'warning' if item.analysis_status in ['QUEUED', 'ANALYSIS', 'CODE_TRANSFORMATION'] else 'info' if item.analysis_status in ['VISUAL_DIFF', 'AWAITING_APPROVAL'] else 'danger' }}">
                                            {% if item.analysis_status in ['ANALYSIS', 'CODE_TRANSFORMATION'] %}
                                                <i class="fas fa-spinner fa-spin me-1"></i>
                                            {% endif %}
                                            {{ item.analysis_status.replace('_', ' ').title() }}
                                            {% if item.target_version %}
                                                → v{{ item.target_version }}
                                            {% endif %}
                                        </span>
                                        {% if item.last_analyzed %}
                                            <br><small class="text-muted">{{ item.last_analyzed.strftime('%Y-%m-%d %H:%M') }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-secondary">Not Analyzed</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.has_analysis and item.latest_job %}
                                        {% if item.analysis_status == 'COMPLETED' %}
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                                    <div class="progress-bar bg-success" style="width: 95%"></div>
                                                </div>
                                                <small>95%</small>
                                            </div>
                                            <div class="mt-1">
                                                <small class="text-muted">
                                                    <span class="badge badge-sm bg-secondary">{{ item.module.version or 'Current' }}</span>
                                                    <i class="fas fa-arrow-right mx-1" style="font-size: 0.7em;"></i>
                                                    <span class="badge badge-sm bg-success">{{ item.target_version }}</span>
                                                </small>
                                            </div>
                                        {% elif item.analysis_status in ['QUEUED', 'ANALYSIS', 'CODE_TRANSFORMATION'] %}
                                            <div class="d-flex align-items-center">
                                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                <small>{{ item.analysis_status|title }}...</small>
                                            </div>
                                        {% elif item.analysis_status == 'FAILED' %}
                                            <span class="text-danger">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                Failed
                                            </span>
                                        {% else %}
                                            <span class="text-info">
                                                <i class="fas fa-clock me-1"></i>
                                                {{ item.analysis_status|title }}
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">Not analyzed</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('main.module_details', module_id=module.id) }}"
                                           class="btn btn-outline-primary" title="View Module Details">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        {% if not item.has_analysis %}
                                            <!-- Start TrueMigrationOrchestrator -->
                                            <a href="{{ url_for('main.orchestrate_migration_form', module_id=module.id) }}"
                                               class="btn btn-success" title="Start Migration via TrueMigrationOrchestrator">
                                                <i class="fas fa-robot me-1"></i>Start Migration
                                            </a>
                                        {% elif item.analysis_status == 'FAILED' %}
                                            <!-- Retry via TrueMigrationOrchestrator -->
                                            <a href="{{ url_for('main.orchestrate_migration_form', module_id=module.id) }}"
                                               class="btn btn-outline-warning" title="Retry Migration via TrueMigrationOrchestrator">
                                                <i class="fas fa-redo"></i>
                                            </a>
                                        {% elif item.analysis_status == 'COMPLETED' %}
                                            <!-- New Migration via TrueMigrationOrchestrator -->
                                            <a href="{{ url_for('main.orchestrate_migration_form', module_id=module.id) }}"
                                               class="btn btn-outline-success" title="Start New Migration via TrueMigrationOrchestrator">
                                                <i class="fas fa-plus me-1"></i>New Migration
                                            </a>

                                        {% endif %}
                                        
                                        {% if item.analysis_status == 'COMPLETED' and item.latest_job %}
                                            <!-- Show additional actions for completed migrations -->
                                            {% if item.latest_job.status == 'COMPLETED' %}
                                                <!-- View Results button -->
                                                <a href="{{ url_for('main.api_visual_diff', job_id=item.latest_job.id) }}"
                                                   class="btn btn-outline-info btn-sm" title="View Migration Results" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                <h4>No Modules Uploaded</h4>
                <p class="text-muted mb-4">Start by uploading some Odoo module files for analysis.</p>
                <a href="{{ url_for('main.upload_modules') }}" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i>Upload Modules
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Legend -->
<div class="row mt-4">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Status Legend</h6>
                <div class="d-flex flex-wrap gap-3">
                    <div>
                        <span class="badge bg-success me-1">Completed</span>
                        Analysis finished successfully
                    </div>
                    <div>
                        <span class="badge bg-info me-1">Analyzing</span>
                        Currently being analyzed
                    </div>
                    <div>
                        <span class="badge bg-warning me-1">Pending</span>
                        Waiting for analysis
                    </div>
                    <div>
                        <span class="badge bg-danger me-1">Error</span>
                        Analysis failed
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Auto-refresh is now handled by main.js initializeAutoRefresh() -->
{% endblock %}
