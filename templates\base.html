<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if title %}{{ title }} - {% endif %}Odoo Module Analyzer</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-cube me-2"></i>
                Odoo Module Analyzer
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.upload_modules_page') }}">
                            <i class="fas fa-upload me-1"></i>Upload Modules
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.migration_orchestrator') }}">
                            <i class="fas fa-cogs me-1"></i>Migration Orchestrator
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.bulk_migration') }}">
                            <i class="fas fa-database me-1"></i>Bulk Migration
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.migration_jobs') }}">
                            <i class="fas fa-tasks me-1"></i>Migration Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('automation.automation_dashboard') }}">
                            <i class="fas fa-robot me-1"></i>Automation
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="moreDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-h me-1"></i>More
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="moreDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('main.analyze_modules') }}">
                                <i class="fas fa-search me-1"></i>Analyze Modules
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.odoo_status') }}">
                                <i class="fas fa-server me-1"></i>Odoo Status
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.health_dashboard') }}">
                                <i class="fas fa-heartbeat me-1"></i>Health Monitor
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.docker_environments') }}">
                                <i class="fab fa-docker me-1"></i>Docker Environments
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.testing_dashboard') }}">
                                <i class="fas fa-flask me-1"></i>Testing
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.github_integration') }}">
                                <i class="fab fa-github me-1"></i>GitHub Integration
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.contributor_upload') }}">
                                <i class="fas fa-users me-1"></i>Contribute
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.ai_providers') }}">
                                <i class="fas fa-brain me-1"></i>AI Settings
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.manual_interventions') }}">
                                <i class="fas fa-gavel me-1"></i>Review Queue
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Odoo Module Analyzer</h5>
                    <p class="mb-0">Analyze and upgrade Odoo modules (v13-v18)</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-code me-1"></i>
                        Built with Flask & Bootstrap
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
