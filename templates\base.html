<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if title %}{{ title }} - {% endif %}Odoo Module Analyzer</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <style>
        /* Sidebar Styles */
        body {
            padding-top: 56px; /* Account for fixed navbar */
        }

        .sidebar {
            position: fixed;
            top: 56px;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            overflow-y: auto;
            background-color: var(--bs-light);
        }

        .sidebar .nav-link {
            color: #333;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin: 0.125rem 0.5rem;
        }

        .sidebar .nav-link:hover {
            color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
            font-weight: 500;
        }

        .sidebar-heading {
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        /* Main content area */
        main {
            margin-top: 0;
            padding-top: 20px;
        }

        /* Mobile responsiveness */
        @media (max-width: 767.98px) {
            .sidebar {
                top: 56px;
                position: relative;
                height: auto;
                padding: 0;
            }
        }

        /* Dark theme adjustments */
        [data-bs-theme="dark"] .sidebar {
            background-color: #212529;
        }

        [data-bs-theme="dark"] .sidebar .nav-link {
            color: #adb5bd;
        }

        [data-bs-theme="dark"] .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        [data-bs-theme="dark"] .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <!-- Top Header Bar -->
    <nav class="navbar navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-cogs me-2"></i>Odoo Upgrade Engine
            </a>
            <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
    </nav>

    <!-- Sidebar Navigation -->
    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <!-- Main Features -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.index') }}">
                                <i class="fas fa-home me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.upload_modules_page') }}">
                                <i class="fas fa-upload me-2"></i>Upload Modules
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.migration_orchestrator') }}">
                                <i class="fas fa-cogs me-2"></i>Migration Orchestrator
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.bulk_migration') }}">
                                <i class="fas fa-database me-2"></i>Bulk Migration
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.migration_jobs') }}">
                                <i class="fas fa-tasks me-2"></i>Migration Jobs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('automation.automation_dashboard') }}">
                                <i class="fas fa-robot me-2"></i>Automation
                            </a>
                        </li>

                        <!-- Divider -->
                        <hr class="my-3">
                        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                            <span>Analysis & Monitoring</span>
                        </h6>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.analyze_modules') }}">
                                <i class="fas fa-search me-2"></i>Analyze Modules
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.odoo_status') }}">
                                <i class="fas fa-server me-2"></i>Odoo Status
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.health_dashboard') }}">
                                <i class="fas fa-heartbeat me-2"></i>Health Monitor
                            </a>
                        </li>

                        <!-- Divider -->
                        <hr class="my-3">
                        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                            <span>Development Tools</span>
                        </h6>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.docker_environments') }}">
                                <i class="fab fa-docker me-2"></i>Docker Environments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.testing_dashboard') }}">
                                <i class="fas fa-flask me-2"></i>Testing
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.github_integration') }}">
                                <i class="fab fa-github me-2"></i>GitHub Integration
                            </a>
                        </li>

                        <!-- Divider -->
                        <hr class="my-3">
                        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                            <span>Configuration</span>
                        </h6>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.contributor_upload') }}">
                                <i class="fas fa-users me-2"></i>Contribute
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.ai_providers') }}">
                                <i class="fas fa-brain me-2"></i>AI Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.manual_interventions') }}">
                                <i class="fas fa-gavel me-2"></i>Review Queue
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="pt-3 pb-2 mb-3">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <!-- Page Content -->
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Odoo Module Analyzer</h5>
                    <p class="mb-0">Analyze and upgrade Odoo modules (v13-v18)</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-code me-1"></i>
                        Built with Flask & Bootstrap
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <script>
        // Highlight active navigation item
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                const linkPath = new URL(link.href).pathname;

                if (currentPath === linkPath ||
                    (currentPath === '/' && linkPath === '/') ||
                    (currentPath.startsWith(linkPath) && linkPath !== '/')) {
                    link.classList.add('active');
                }
            });
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
