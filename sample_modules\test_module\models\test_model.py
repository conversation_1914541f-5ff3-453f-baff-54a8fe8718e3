from odoo import models, fields, api
from odoo.tools import config

class TestModel(models.Model):
    _name = 'test.model'
    _description = 'Test Model for Migration'
    
    name = fields.Char('Name', required=True, help='Help text for name')
    value = fields.Float('Value', help='Help text for value')
    
    @api.multi
    def compute_total(self):
        for record in self:
            self.total = self.value * 2
    
    @api.multi  
    def old_method(self):
        return self.sudo().write({'value': 100})
        
    def another_method(self):
        # This should be updated in v16->v17
        user_id = SUPERUSER_ID
        return user_id