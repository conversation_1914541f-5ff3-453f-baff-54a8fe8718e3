<?xml version="1.0" encoding="utf-8"?>
<openerp>
    <data>
        <!-- Legacy view structure - old XML format -->
        <record id="view_order_form_legacy" model="ir.ui.view">
            <field name="name">sale.order.form.legacy</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <field name="partner_id" position="after">
                    <field name="legacy_field"/>
                    <field name="discount_amount"/>
                </field>
                <xpath expr="//field[@name='amount_total']" position="after">
                    <field name="legacy_total" readonly="1"/>
                </xpath>
                <xpath expr="//header" position="inside">
                    <button name="action_legacy_confirm" type="object" string="Legacy Confirm" 
                            states="draft" class="oe_highlight"/>
                </xpath>
            </field>
        </record>

        <!-- Old menu structure -->
        <menuitem id="menu_legacy_sales" name="Legacy Sales" parent="sale.sale_menu_root" sequence="10"/>
        
        <record id="action_legacy_orders" model="ir.actions.act_window">
            <field name="name">Legacy Orders</field>
            <field name="res_model">sale.order</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('legacy_field', '!=', False)]</field>
        </record>
        
        <menuitem id="menu_legacy_orders" name="Legacy Orders" 
                  parent="menu_legacy_sales" action="action_legacy_orders"/>
    </data>
</openerp>