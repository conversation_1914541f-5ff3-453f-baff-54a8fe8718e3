#!/usr/bin/env python3
"""
Create Test Migration Jobs

This script creates test migration jobs so we can test the buttons functionality.
"""

import os
import sys
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from models import OdooModule, MigrationJob

def create_test_jobs():
    """Create test migration jobs"""
    app = create_app()
    
    with app.app_context():
        print("🔧 Creating test migration jobs...")
        
        # Create test modules first
        test_modules = [
            {'name': 'test_sales_module', 'version': '16.0', 'path': 'uploads/test_sales_module.zip'},
            {'name': 'test_inventory_module', 'version': '15.0', 'path': 'uploads/test_inventory_module.zip'},
            {'name': 'test_hr_module', 'version': '17.0', 'path': 'uploads/test_hr_module.zip'}
        ]
        
        created_jobs = 0
        
        for module_data in test_modules:
            # Create or get module
            module = OdooModule.query.filter_by(name=module_data['name']).first()
            if not module:
                module = OdooModule(
                    name=module_data['name'],
                    version=module_data['version'],
                    path=module_data['path'],
                    timestamp=datetime.now()
                )
                db.session.add(module)
                db.session.flush()  # Get the ID
            
            # Create migration job
            existing_job = MigrationJob.query.filter_by(module_id=module.id).first()
            if not existing_job:
                job = MigrationJob(
                    module_id=module.id,
                    target_version='18.0',
                    status='QUEUED',
                    timestamp=datetime.now(),
                    log=f"Test migration job for {module_data['name']}"
                )
                db.session.add(job)
                created_jobs += 1
                print(f"   ✅ Created job for {module_data['name']}")
            else:
                print(f"   ⚠️  Job already exists for {module_data['name']}")
        
        # Create one job in different status for testing
        if created_jobs > 0:
            # Make one job "in progress"
            jobs = MigrationJob.query.limit(2).all()
            if len(jobs) > 1:
                jobs[1].status = 'ANALYSIS'
                jobs[1].log = "Test job in analysis phase"
                print(f"   ✅ Set {jobs[1].module.name} to ANALYSIS status")
        
        db.session.commit()
        
        print(f"\n🎉 Successfully created {created_jobs} test migration jobs")
        
        # Show all jobs
        all_jobs = MigrationJob.query.all()
        print(f"\n📋 Current migration jobs ({len(all_jobs)} total):")
        for job in all_jobs:
            print(f"   - Job {job.id}: {job.module.name} → {job.target_version} ({job.status})")
        
        return True

if __name__ == "__main__":
    success = create_test_jobs()
    sys.exit(0 if success else 1)
