# JavaScript Widget Demo Module

This module demonstrates comprehensive file type analysis for Odoo 18 compatibility testing.

## Features

- **Modern JavaScript**: ES6+ syntax with OWL framework components
- **Legacy Pattern Detection**: Identifies deprecated jQuery and OpenERP patterns
- **SCSS Styling**: Modern CSS with variables, mixins, and responsive design
- **Test Coverage**: QUnit and Mocha/Jasmine test patterns
- **XML Templates**: Odoo 18 compatible view templates

## File Structure

```
js_widget_module/
├── __manifest__.py          # Module manifest
├── README.md               # Documentation
├── config.json            # Configuration file
├── static/
│   └── src/
│       ├── js/
│       │   ├── demo_widget.js      # Main widget with modern patterns
│       │   └── test_demo_widget.js # Test file with various frameworks
│       └── css/
│           └── demo_widget.scss    # SCSS styling
└── views/
    └── assets.xml          # XML templates and assets
```

## Compatibility Analysis

The module analyzer will detect:

### JavaScript Issues
- Deprecated OpenERP web client references
- Old jQuery document ready patterns
- Legacy web.Widget usage
- Outdated RPC patterns

### JavaScript Warnings
- ES6+ syntax requiring transpilation
- OWL framework compatibility checks
- Test framework compatibility

### Modern Patterns
- @odoo/owl framework usage
- ES6 classes and arrow functions
- Modern async/await patterns
- Template literals and destructuring

## Installation

This is a demo module for testing the analysis system. In a real Odoo environment:

1. Copy to addons directory
2. Update module list
3. Install the module

## Testing

The module includes test files to verify the analyzer can detect:
- QUnit test patterns
- Mocha/Jasmine test syntax
- Legacy jQuery test methods

## Development Notes

This module intentionally includes both modern and legacy patterns to thoroughly test the compatibility analysis system.