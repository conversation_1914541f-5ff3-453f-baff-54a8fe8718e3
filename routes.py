# routes.py
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from werkzeug.utils import secure_filename
from models import db, OdooModule, MigrationJob, DiffReport
from module_analyzer import ModuleAnalyzer
from tasks import start_migration_task, continue_migration_task
import os
import zipfile

main_routes = Blueprint('main', __name__)
UPLOAD_FOLDER = 'uploads'

@main_routes.route('/')
def index():
    modules = OdooModule.query.order_by(OdooModule.name).all()

    # Calculate dashboard statistics
    total_modules = len(modules)
    analyzed_modules = len([m for m in modules if m.analyses])  # Modules with analysis records
    pending_modules = total_modules - analyzed_modules  # Modules without analysis
    error_modules = 0  # For now, we'll set this to 0

    # Get recent modules (last 10)
    recent_modules = OdooModule.query.order_by(OdooModule.timestamp.desc()).limit(10).all()

    # Mock odoo installation status
    odoo_installation = {'status': 'active'}  # For now, assume Odoo is active

    return render_template('index.html',
                         modules=modules,
                         total_modules=total_modules,
                         analyzed_modules=analyzed_modules,
                         pending_modules=pending_modules,
                         error_modules=error_modules,
                         recent_modules=recent_modules,
                         odoo_installation=odoo_installation)

@main_routes.route('/upload', methods=['POST'])
def upload_modules():
    if 'modules' not in request.files:
        flash('No file part in the request.', 'danger')
        return redirect(request.url)
    
    files = request.files.getlist('modules')
    if not files or files[0].filename == '':
        flash('No files selected for upload.', 'warning')
        return redirect(url_for('main.index'))

    for file in files:
        if file and file.filename.endswith('.zip'):
            filename = secure_filename(file.filename)
            module_path = os.path.join(UPLOAD_FOLDER, filename)
            file.save(module_path)
            try:
                analyzer = ModuleAnalyzer(module_path)
                manifest = analyzer.get_manifest()
                module_name = manifest.get('name', os.path.splitext(filename)[0])
                module_version = manifest.get('version', 'N/A')
                existing_module = OdooModule.query.filter_by(name=module_name).first()
                if not existing_module:
                    new_module = OdooModule(name=module_name, version=module_version, path=module_path)
                    db.session.add(new_module)
                    flash(f"Module '{module_name}' uploaded and registered.", 'success')
                else:
                    existing_module.path = module_path
                    existing_module.version = module_version
                    flash(f"Module '{module_name}' re-uploaded and path updated.", 'info')
                db.session.commit()
            except (zipfile.BadZipFile, FileNotFoundError, Exception) as e:
                flash(f"Error processing {filename}: {str(e)}", 'danger')
                if os.path.exists(module_path):
                    os.remove(module_path)
    return redirect(url_for('main.index'))

@main_routes.route('/module/<int:module_id>')
def module_details(module_id):
    module = OdooModule.query.get_or_404(module_id)
    migration_jobs = MigrationJob.query.filter_by(module_id=module.id).order_by(MigrationJob.timestamp.desc()).all()
    return render_template('module_details.html', module=module, migration_jobs=migration_jobs)

@main_routes.route('/migration_jobs', methods=['POST'])
def create_migration_job():
    module_id = request.form.get('module_id')
    target_version = request.form.get('target_version')
    module = OdooModule.query.get_or_404(module_id)
    new_job = MigrationJob(module_id=module.id, target_version=target_version, status='QUEUED')
    db.session.add(new_job)
    db.session.commit()
    start_migration_task.delay(new_job.id)
    flash(f"Migration job for '{module.name}' to v{target_version} has been queued successfully!", 'success')
    return redirect(url_for('main.module_details', module_id=module.id))

@main_routes.route('/migration/<int:job_id>/review')
def view_diff(job_id):
    job = MigrationJob.query.get_or_404(job_id)
    if job.status != 'AWAITING_APPROVAL':
        flash('This migration job is not currently awaiting approval.', 'warning')
        return redirect(url_for('main.module_details', module_id=job.module_id))
    diff_report = DiffReport.query.filter_by(migration_job_id=job.id).first_or_404()
    return render_template('view_diff.html', job=job, diff_report=diff_report)

@main_routes.route('/migration/<int:job_id>/approve', methods=['POST'])
def approve_migration(job_id):
    job = MigrationJob.query.get_or_404(job_id)
    if job.status != 'AWAITING_APPROVAL':
        flash('This job cannot be approved as it is not awaiting approval.', 'danger')
        return redirect(url_for('main.module_details', module_id=job.module_id))
    job.status = 'DIFF_APPROVED'
    db.session.commit()
    continue_migration_task.delay(job.id)
    flash(f'Migration for {job.module.name} approved. Resuming with database migration and testing.', 'success')
    return redirect(url_for('main.module_details', module_id=job.module_id))

@main_routes.route('/api/status/<int:job_id>')
def get_job_status(job_id):
    job = MigrationJob.query.get_or_404(job_id)
    return jsonify({
        'job_id': job.id,
        'status': job.status,
        'module_name': job.module.name,
        'target_version': job.target_version,
        'timestamp': job.timestamp.isoformat()
    })

@main_routes.route('/api/migration-jobs')
def api_migration_jobs():
    """API endpoint for migration jobs data"""
    try:
        jobs = MigrationJob.query.order_by(MigrationJob.timestamp.desc()).all()
        jobs_data = []

        for job in jobs:
            job_data = {
                'id': job.id,
                'module_name': job.module.name,
                'target_version': job.target_version,
                'status': job.status,
                'timestamp': job.timestamp.isoformat(),
                'log': job.log or '',
                # Mock additional data that the frontend expects
                'progress': 100 if job.status == 'COMPLETED' else 50 if job.status in ['ANALYSIS', 'CODE_TRANSFORMATION'] else 0,
                'semantic_analysis_data': {
                    'semantic_issues': [],
                    'compatibility_score': 85
                },
                'visual_diff_data': {
                    'changes_count': 0,
                    'files_modified': []
                }
            }
            jobs_data.append(job_data)

        return jsonify({
            'success': True,
            'jobs': jobs_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@main_routes.route('/api/docker-environments')
def api_docker_environments():
    """API endpoint for Docker environments data"""
    try:
        # Mock Docker environments data
        environments = [
            {
                'id': 1,
                'odoo_version': '18.0',
                'container_name': 'odoo-18-dev',
                'status': 'running',
                'port': 8018,
                'created_at': '2025-07-01T10:00:00Z',
                'last_used': '2025-07-05T15:30:00Z'
            },
            {
                'id': 2,
                'odoo_version': '17.0',
                'container_name': 'odoo-17-dev',
                'status': 'stopped',
                'port': 8017,
                'created_at': '2025-07-01T10:00:00Z',
                'last_used': '2025-07-04T12:15:00Z'
            }
        ]

        statistics = {
            'total_environments': len(environments),
            'running_environments': len([e for e in environments if e['status'] == 'running']),
            'stopped_environments': len([e for e in environments if e['status'] == 'stopped']),
            'docker_available': True
        }

        return jsonify({
            'success': True,
            'environments': environments,
            'statistics': statistics
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Core Feature Routes - Based on README Architecture Components

@main_routes.route('/upload_modules')
def upload_modules_page():
    """Module Upload & Analysis - Component 1"""
    return render_template('upload_modules.html')

@main_routes.route('/migration_orchestrator')
def migration_orchestrator():
    """Migration Orchestrator - Component 4"""
    # Show a module selection page instead of the complex orchestrator template
    modules = OdooModule.query.all()
    return render_template('migration_jobs.html', modules=modules)

@main_routes.route('/bulk_migration')
def bulk_migration():
    """Bulk Migration Manager - Component 5"""
    return render_template('bulk_migration.html')

@main_routes.route('/migration_jobs')
def migration_jobs():
    """Migration Jobs Dashboard"""
    jobs = MigrationJob.query.order_by(MigrationJob.timestamp.desc()).all()
    return render_template('migration_jobs.html', jobs=jobs)

# Additional routes needed by index.html template

@main_routes.route('/docker_environments')
def docker_environments():
    """Docker Environments Management"""
    return render_template('docker_environments.html')

@main_routes.route('/analyze_modules')
def analyze_modules():
    """Module Analysis Dashboard"""
    modules = OdooModule.query.all()
    return render_template('analyze_modules.html', modules=modules)

@main_routes.route('/install_odoo')
def install_odoo():
    """Odoo Installation Management"""
    return render_template('odoo_status.html')

@main_routes.route('/analyze_all')
def analyze_all():
    """Analyze All Pending Modules"""
    flash('Bulk analysis feature coming soon!', 'info')
    return redirect(url_for('main.index'))

@main_routes.route('/orchestrate_migration_form/<int:module_id>')
def orchestrate_migration_form(module_id):
    """Migration Orchestrator Form for specific module"""
    module = OdooModule.query.get_or_404(module_id)
    # Add mock data to match template expectations
    module.filename = module.name  # Use name as filename
    module.upload_date = module.timestamp  # Use timestamp as upload_date
    module.file_size = 1024 * 1024  # Mock file size (1MB)

    # Get analysis data if available
    analysis = module.analyses[0] if module.analyses else None

    return render_template('migration_orchestrator.html', module=module, analysis=analysis)

# Additional missing routes

@main_routes.route('/contributor_upload')
def contributor_upload():
    """Contributor Upload Form"""
    return render_template('contributor_upload.html')

@main_routes.route('/testing_dashboard')
def testing_dashboard():
    """Testing Dashboard"""
    # Mock testing configuration
    testing_config = {
        'docker_available': True,  # Assume Docker is available
        'runbot_configured': False,  # Runbot not configured yet
        'ai_available': True,  # AI analysis available
        'test_environments': ['development', 'staging']
    }

    # Mock testing statistics
    testing_stats = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'pending_tests': 0
    }

    return render_template('testing/dashboard.html',
                         testing_config=testing_config,
                         testing_stats=testing_stats,
                         testing_available=True)

@main_routes.route('/ai_providers')
def ai_providers():
    """AI Providers Configuration"""
    return render_template('ai_providers.html')

@main_routes.route('/github_integration')
def github_integration():
    """GitHub Integration"""
    return render_template('github_integration.html')

@main_routes.route('/manual_interventions')
def manual_interventions():
    """Manual Interventions / Review Queue"""
    return render_template('manual_interventions.html')

@main_routes.route('/health_dashboard')
def health_dashboard():
    """System Health Dashboard"""
    return render_template('health_dashboard.html')

@main_routes.route('/odoo_status')
def odoo_status():
    """Odoo Installation Status"""
    return render_template('odoo_status.html')

# Duplicate module_details route removed - using the original at /module/<int:module_id>

# Duplicate view_diff route removed - using the original at /migration/<int:job_id>/review

# Automation routes are handled by automation_blueprint in automation_integration.py