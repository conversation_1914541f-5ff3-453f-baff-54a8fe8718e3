{% extends "base.html" %}

{% block title %}Bulk Database Migration{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-database me-2"></i>
                Bulk Database Migration
            </h1>
            <p class="lead text-muted">
                Migrate production Odoo databases with 200+ modules efficiently
            </p>
        </div>
    </div>

    <!-- Connection Setup -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plug me-2"></i>
                        Database Connection
                    </h5>
                </div>
                <div class="card-body">
                    <form id="connectionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dbHost" class="form-label">Database Host</label>
                                    <input type="text" class="form-control" id="dbHost" 
                                           placeholder="localhost" value="localhost">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dbPort" class="form-label">Port</label>
                                    <input type="number" class="form-control" id="dbPort" 
                                           placeholder="5432" value="5432">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="dbName" class="form-label">Database Name</label>
                                    <input type="text" class="form-control" id="dbName" 
                                           placeholder="odoo_production" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="dbUser" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="dbUser" 
                                           placeholder="odoo" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="dbPassword" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="dbPassword" 
                                           placeholder="********" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sourceVersion" class="form-label">Current Odoo Version</label>
                                    <select class="form-select" id="sourceVersion" required>
                                        <option value="">Select current version</option>
                                        <option value="13.0">Odoo 13.0</option>
                                        <option value="14.0">Odoo 14.0</option>
                                        <option value="15.0">Odoo 15.0</option>
                                        <option value="16.0">Odoo 16.0</option>
                                        <option value="17.0">Odoo 17.0</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="targetVersion" class="form-label">Target Odoo Version</label>
                                    <select class="form-select" id="targetVersion" required>
                                        <option value="">Select target version</option>
                                        <option value="14.0">Odoo 14.0</option>
                                        <option value="15.0">Odoo 15.0</option>
                                        <option value="16.0">Odoo 16.0</option>
                                        <option value="17.0">Odoo 17.0</option>
                                        <option value="18.0">Odoo 18.0</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="testConnection">
                                <i class="fas fa-check-circle me-1"></i>Test Connection
                            </button>
                            <button type="button" class="btn btn-success" id="discoverModules">
                                <i class="fas fa-search me-1"></i>Discover Modules
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Discovery Results -->
    <div class="row mb-4" id="moduleDiscoverySection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cubes me-2"></i>
                        Discovered Modules
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h2 class="text-primary mb-0" id="totalModules">0</h2>
                                    <small class="text-muted">Total Modules</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h2 class="text-success mb-0" id="standardModules">0</h2>
                                    <small class="text-muted">Standard Modules</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h2 class="text-warning mb-0" id="customModules">0</h2>
                                    <small class="text-muted">Custom Modules</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h2 class="text-info mb-0" id="estimatedTime">0h</h2>
                                    <small class="text-muted">Estimated Time</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <button type="button" class="btn btn-info" id="analyzeComplexity">
                                <i class="fas fa-analytics me-1"></i>Analyze Migration Complexity
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" data-bs-toggle="collapse" 
                                    data-bs-target="#moduleList">
                                <i class="fas fa-list me-1"></i>View Module List
                            </button>
                        </div>
                    </div>
                    
                    <div class="collapse mt-3" id="moduleList">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Module Name</th>
                                        <th>State</th>
                                        <th>Version</th>
                                        <th>Type</th>
                                        <th>Dependencies</th>
                                    </tr>
                                </thead>
                                <tbody id="moduleTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Migration Analysis -->
    <div class="row mb-4" id="migrationAnalysisSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Migration Analysis
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h3 class="text-success mb-0" id="simpleCount">0</h3>
                                    <small class="text-muted">Simple Migrations</small>
                                    <div class="small text-muted">~6 min each</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h3 class="text-warning mb-0" id="mediumCount">0</h3>
                                    <small class="text-muted">Medium Complexity</small>
                                    <div class="small text-muted">~30 min each</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <h3 class="text-danger mb-0" id="complexCount">0</h3>
                                    <small class="text-muted">Complex Migrations</small>
                                    <div class="small text-muted">~2 hours each</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-dark">
                                <div class="card-body text-center">
                                    <h3 class="text-dark mb-0" id="criticalCount">0</h3>
                                    <small class="text-muted">Critical/Manual</small>
                                    <div class="small text-muted">~4 hours each</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Risk Assessment</h6>
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <div class="progress">
                                                <div class="progress-bar" id="riskBar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                        <span class="ms-2" id="riskLevel">Low</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Migration Phases</h6>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-layer-group me-2"></i>
                                        <span id="phaseCount">0</span> phases planned
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <button type="button" class="btn btn-primary" id="createMigrationPlan">
                                <i class="fas fa-clipboard-list me-1"></i>Create Migration Plan
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Migration Plan -->
    <div class="row mb-4" id="migrationPlanSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard-list me-2"></i>
                        Migration Plan
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Plan Overview</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Plan ID:</strong></td>
                                                <td id="planId">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Migration Path:</strong></td>
                                                <td><span id="migrationPath">-</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Total Modules:</strong></td>
                                                <td id="planModuleCount">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Estimated Duration:</strong></td>
                                                <td id="planDuration">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Risk Level:</strong></td>
                                                <td><span class="badge" id="planRiskBadge">-</span></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Migration Options</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeDatabaseMigration" checked>
                                        <label class="form-check-label" for="includeDatabaseMigration">
                                            Include Database Migration
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeModuleUpgrade" checked>
                                        <label class="form-check-label" for="includeModuleUpgrade">
                                            Include Module Code Upgrade
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="createBackup" checked>
                                        <label class="form-check-label" for="createBackup">
                                            Create Full Backup
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dryRun">
                                        <label class="form-check-label" for="dryRun">
                                            Dry Run (No Changes)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6>Migration Phases</h6>
                            <div class="accordion" id="phasesAccordion">
                                <!-- Phases will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-success" id="executeMigration">
                                    <i class="fas fa-play me-1"></i>Execute Migration
                                </button>
                                <button type="button" class="btn btn-outline-info" id="dryRunMigration">
                                    <i class="fas fa-eye me-1"></i>Dry Run
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="downloadPlan">
                                    <i class="fas fa-download me-1"></i>Download Plan
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Migration Execution -->
    <div class="row mb-4" id="migrationExecutionSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Migration Execution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Overall Progress</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar" id="overallProgress" role="progressbar" style="width: 0%">
                                            <span id="overallProgressText">0%</span>
                                        </div>
                                    </div>
                                    <div class="small text-muted">
                                        Phase <span id="currentPhase">0</span> of <span id="totalPhases">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Current Status</h6>
                                    <div class="d-flex align-items-center">
                                        <div class="spinner-border spinner-border-sm me-2" id="statusSpinner" style="display: none;"></div>
                                        <span id="currentStatus">Ready to start</span>
                                    </div>
                                    <div class="small text-muted mt-1">
                                        <span id="modulesProcessed">0</span> of <span id="totalModulesExecution">0</span> modules processed
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6>Live Log</h6>
                            <div class="card">
                                <div class="card-body">
                                    <div id="liveLog" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                                        <div class="text-muted">Migration log will appear here...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-warning" id="pauseMigration" disabled>
                                    <i class="fas fa-pause me-1"></i>Pause
                                </button>
                                <button type="button" class="btn btn-danger" id="stopMigration" disabled>
                                    <i class="fas fa-stop me-1"></i>Stop
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="downloadLogs">
                                    <i class="fas fa-download me-1"></i>Download Logs
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let discoveredModules = [];
    let migrationAnalysis = null;
    let migrationPlan = null;
    let migrationStatus = null;
    
    // Test database connection
    document.getElementById('testConnection').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Testing...';
        btn.disabled = true;
        
        const connectionData = {
            host: document.getElementById('dbHost').value,
            port: document.getElementById('dbPort').value,
            database: document.getElementById('dbName').value,
            user: document.getElementById('dbUser').value,
            password: document.getElementById('dbPassword').value
        };
        
        fetch('/api/test-db-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(connectionData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                btn.className = 'btn btn-success';
                btn.innerHTML = '<i class="fas fa-check me-1"></i>Connection OK';
                setTimeout(() => {
                    btn.className = 'btn btn-outline-primary';
                    btn.innerHTML = originalText;
                }, 3000);
            } else {
                btn.className = 'btn btn-danger';
                btn.innerHTML = '<i class="fas fa-times me-1"></i>Connection Failed';
                setTimeout(() => {
                    btn.className = 'btn btn-outline-primary';
                    btn.innerHTML = originalText;
                }, 3000);
            }
        })
        .catch(error => {
            btn.className = 'btn btn-danger';
            btn.innerHTML = '<i class="fas fa-times me-1"></i>Error';
            setTimeout(() => {
                btn.className = 'btn btn-outline-primary';
                btn.innerHTML = originalText;
            }, 3000);
        })
        .finally(() => {
            btn.disabled = false;
        });
    });
    
    // Discover modules
    document.getElementById('discoverModules').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Discovering...';
        btn.disabled = true;
        
        const connectionData = {
            host: document.getElementById('dbHost').value,
            port: document.getElementById('dbPort').value,
            database: document.getElementById('dbName').value,
            user: document.getElementById('dbUser').value,
            password: document.getElementById('dbPassword').value
        };
        
        fetch('/api/discover-modules', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(connectionData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                discoveredModules = data.modules;
                displayModuleDiscovery(data);
                document.getElementById('moduleDiscoverySection').style.display = 'block';
            } else {
                alert('Failed to discover modules: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error discovering modules: ' + error);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    });
    
    // Analyze migration complexity
    document.getElementById('analyzeComplexity').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Analyzing...';
        btn.disabled = true;
        
        const analysisData = {
            modules: discoveredModules,
            source_version: document.getElementById('sourceVersion').value,
            target_version: document.getElementById('targetVersion').value
        };
        
        fetch('/api/analyze-migration-complexity', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(analysisData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                migrationAnalysis = data.analysis;
                displayMigrationAnalysis(data.analysis);
                document.getElementById('migrationAnalysisSection').style.display = 'block';
            } else {
                alert('Failed to analyze migration complexity: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error analyzing migration: ' + error);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    });
    
    // Create migration plan
    document.getElementById('createMigrationPlan').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating Plan...';
        btn.disabled = true;
        
        fetch('/api/create-migration-plan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({analysis: migrationAnalysis})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                migrationPlan = data.plan;
                displayMigrationPlan(data.plan);
                document.getElementById('migrationPlanSection').style.display = 'block';
            } else {
                alert('Failed to create migration plan: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error creating migration plan: ' + error);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    });
    
    // Execute migration
    document.getElementById('executeMigration').addEventListener('click', function() {
        if (!confirm('Are you sure you want to start the migration? This will modify your database.')) {
            return;
        }
        
        const options = {
            include_database: document.getElementById('includeDatabaseMigration').checked,
            include_modules: document.getElementById('includeModuleUpgrade').checked,
            create_backup: document.getElementById('createBackup').checked,
            dry_run: document.getElementById('dryRun').checked
        };
        
        startMigrationExecution(options);
    });
    
    // Dry run migration
    document.getElementById('dryRunMigration').addEventListener('click', function() {
        const options = {
            include_database: document.getElementById('includeDatabaseMigration').checked,
            include_modules: document.getElementById('includeModuleUpgrade').checked,
            create_backup: false,
            dry_run: true
        };
        
        startMigrationExecution(options);
    });
    
    function displayModuleDiscovery(data) {
        document.getElementById('totalModules').textContent = data.modules.length;
        document.getElementById('standardModules').textContent = data.standard_count || 0;
        document.getElementById('customModules').textContent = data.custom_count || 0;
        document.getElementById('estimatedTime').textContent = (data.estimated_hours || 0) + 'h';
        
        // Populate module table
        const tbody = document.getElementById('moduleTableBody');
        tbody.innerHTML = '';
        
        data.modules.forEach(module => {
            const row = tbody.insertRow();
            row.innerHTML = `
                <td>${module.name}</td>
                <td><span class="badge bg-${module.state === 'installed' ? 'success' : 'warning'}">${module.state}</span></td>
                <td>${module.version || 'Unknown'}</td>
                <td><span class="badge bg-${module.is_standard ? 'primary' : 'secondary'}">${module.is_standard ? 'Standard' : 'Custom'}</span></td>
                <td>${module.depends ? module.depends.join(', ') : 'None'}</td>
            `;
        });
    }
    
    function displayMigrationAnalysis(analysis) {
        document.getElementById('simpleCount').textContent = analysis.complexity_groups.simple.length;
        document.getElementById('mediumCount').textContent = analysis.complexity_groups.medium.length;
        document.getElementById('complexCount').textContent = analysis.complexity_groups.complex.length;
        document.getElementById('criticalCount').textContent = analysis.complexity_groups.critical.length;
        
        // Risk assessment
        const riskLevel = analysis.risk_assessment;
        const riskBar = document.getElementById('riskBar');
        const riskLevelSpan = document.getElementById('riskLevel');
        
        if (riskLevel === 'low') {
            riskBar.style.width = '33%';
            riskBar.className = 'progress-bar bg-success';
            riskLevelSpan.textContent = 'Low Risk';
        } else if (riskLevel === 'medium') {
            riskBar.style.width = '66%';
            riskBar.className = 'progress-bar bg-warning';
            riskLevelSpan.textContent = 'Medium Risk';
        } else {
            riskBar.style.width = '100%';
            riskBar.className = 'progress-bar bg-danger';
            riskLevelSpan.textContent = 'High Risk';
        }
        
        document.getElementById('phaseCount').textContent = analysis.migration_phases.length;
    }
    
    function displayMigrationPlan(plan) {
        document.getElementById('planId').textContent = plan.plan_id;
        document.getElementById('migrationPath').textContent = `${plan.source_version} → ${plan.target_version}`;
        document.getElementById('planModuleCount').textContent = plan.total_modules;
        document.getElementById('planDuration').textContent = plan.estimated_duration + ' hours';
        
        // Risk badge
        const riskBadge = document.getElementById('planRiskBadge');
        riskBadge.textContent = plan.risk_level;
        riskBadge.className = `badge bg-${plan.risk_level === 'low' ? 'success' : plan.risk_level === 'medium' ? 'warning' : 'danger'}`;
        
        // Populate phases
        const accordion = document.getElementById('phasesAccordion');
        accordion.innerHTML = '';
        
        plan.phases.forEach((phase, index) => {
            const phaseHtml = `
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#phase${index}" aria-expanded="false">
                            Phase ${phase.phase_number}: ${phase.modules.length} modules
                            <span class="badge bg-secondary ms-2">${phase.estimated_duration} min</span>
                        </button>
                    </h2>
                    <div id="phase${index}" class="accordion-collapse collapse" data-bs-parent="#phasesAccordion">
                        <div class="accordion-body">
                            <p><strong>Modules:</strong> ${phase.modules.join(', ')}</p>
                            <p><strong>Pre-migration tasks:</strong></p>
                            <ul>
                                ${phase.pre_migration_tasks.map(task => `<li>${task}</li>`).join('')}
                            </ul>
                            <p><strong>Post-migration tasks:</strong></p>
                            <ul>
                                ${phase.post_migration_tasks.map(task => `<li>${task}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            accordion.innerHTML += phaseHtml;
        });
    }
    
    function startMigrationExecution(options) {
        document.getElementById('migrationExecutionSection').style.display = 'block';
        document.getElementById('statusSpinner').style.display = 'inline-block';
        document.getElementById('currentStatus').textContent = 'Starting migration...';
        
        // Start migration
        fetch('/api/execute-bulk-migration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                plan: migrationPlan,
                options: options
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Start progress monitoring
                startProgressMonitoring();
            } else {
                alert('Failed to start migration: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error starting migration: ' + error);
        });
    }
    
    function startProgressMonitoring() {
        const interval = setInterval(() => {
            fetch('/api/migration-status')
                .then(response => response.json())
                .then(data => {
                    updateMigrationProgress(data.status);
                    
                    if (data.status.status === 'completed' || data.status.status === 'failed') {
                        clearInterval(interval);
                        document.getElementById('statusSpinner').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error fetching migration status:', error);
                });
        }, 2000);
    }
    
    function updateMigrationProgress(status) {
        const progress = (status.current_phase / status.total_phases) * 100;
        document.getElementById('overallProgress').style.width = progress + '%';
        document.getElementById('overallProgressText').textContent = Math.round(progress) + '%';
        
        document.getElementById('currentPhase').textContent = status.current_phase;
        document.getElementById('totalPhases').textContent = status.total_phases;
        document.getElementById('currentStatus').textContent = status.status;
        document.getElementById('modulesProcessed').textContent = status.processed_modules;
        document.getElementById('totalModulesExecution').textContent = status.total_modules;
        
        // Update log
        if (status.recent_logs) {
            const logDiv = document.getElementById('liveLog');
            status.recent_logs.forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${log}`;
                logDiv.appendChild(logEntry);
            });
            logDiv.scrollTop = logDiv.scrollHeight;
        }
    }
});
</script>
{% endblock %}