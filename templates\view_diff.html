{% extends "base.html" %}

{% block title %}Review Migration for {{ job.module.name }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="card mb-4">
        <div class="card-header bg-warning">
            <h2 class="h4 mb-0">Review Migration: <strong>{{ job.module.name }}</strong> to v{{ job.target_version }}</h2>
        </div>
        <div class="card-body">
            <p>
                The automated code transformation is complete. Please review the changes below. Approving this will
                initiate the **irreversible** database migration and testing phases.
            </p>
            <ul>
                <li><strong>Job ID:</strong> {{ job.id }}</li>
                <li><strong>Module:</strong> {{ job.module.name }} ({{ job.module.version }})</li>
                <li><strong>Target Version:</strong> {{ job.target_version }}</li>
            </ul>
            <div class="d-flex gap-2 mt-3">
                <form action="{{ url_for('main.approve_migration', job_id=job.id) }}" method="POST" onsubmit="return confirm('Are you sure you want to approve these changes and proceed with the database migration? This cannot be undone.');">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check-circle me-2"></i>Approve & Continue
                    </button>
                </form>
                <a href="{{ url_for('main.module_details', module_id=job.module_id) }}" class="btn btn-danger">
                    <i class="fas fa-times-circle me-2"></i>Reject & Go Back
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-dark text-white">
            <h3 class="h5 mb-0">Visual Difference Report</h3>
        </div>
        <div class="card-body p-0">
            <div style="width: 100%; height: 80vh; border: 0;">
                <iframe srcdoc="{{ diff_report.content|e }}" style="width: 100%; height: 100%; border: 0;"></iframe>
            </div>
        </div>
    </div>
</div>
{% endblock %}