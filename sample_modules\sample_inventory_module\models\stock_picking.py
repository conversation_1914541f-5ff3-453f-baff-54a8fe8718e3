from odoo import models, fields, api
from odoo.exceptions import UserError


class StockPicking(models.Model):
    _inherit = 'stock.picking'
    
    # Enhanced picking functionality
    priority_level = fields.Selection([
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ], string='Priority Level', default='normal')
    
    estimated_completion_time = fields.Datetime(string='Estimated Completion')
    actual_completion_time = fields.Datetime(string='Actual Completion')
    picker_id = fields.Many2one('res.users', string='Assigned Picker')
    
    # Quality control
    quality_check_required = fields.Boolean(string='Quality Check Required', default=False)
    quality_check_completed = fields.Boolean(string='Quality Check Completed', default=False)
    quality_notes = fields.Text(string='Quality Notes')
    
    # Performance metrics
    pick_efficiency = fields.Float(string='Pick Efficiency (%)', compute='_compute_pick_efficiency')
    total_pick_time = fields.Float(string='Total Pick Time (hours)', compute='_compute_pick_time')
    
    @api.depends('estimated_completion_time', 'actual_completion_time')
    def _compute_pick_efficiency(self):
        for picking in self:
            if picking.estimated_completion_time and picking.actual_completion_time:
                estimated_duration = (picking.estimated_completion_time - picking.date).total_seconds() / 3600
                actual_duration = (picking.actual_completion_time - picking.date).total_seconds() / 3600
                if actual_duration > 0:
                    picking.pick_efficiency = (estimated_duration / actual_duration) * 100
                else:
                    picking.pick_efficiency = 0.0
            else:
                picking.pick_efficiency = 0.0
    
    @api.depends('date', 'actual_completion_time')
    def _compute_pick_time(self):
        for picking in self:
            if picking.date and picking.actual_completion_time:
                picking.total_pick_time = (picking.actual_completion_time - picking.date).total_seconds() / 3600
            else:
                picking.total_pick_time = 0.0
    
    def action_assign_picker(self):
        """Assign a picker to this picking"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Assign Picker',
            'res_model': 'stock.picking.assign.wizard',
            'view_mode': 'form',
            'context': {'default_picking_id': self.id},
            'target': 'new',
        }
    
    def action_start_quality_check(self):
        """Start quality check process"""
        self.ensure_one()
        if not self.quality_check_required:
            raise UserError("Quality check is not required for this picking.")
        
        return {
            'type': 'ir.actions.act_window',
            'name': 'Quality Check',
            'res_model': 'stock.picking.quality.wizard',
            'view_mode': 'form',
            'context': {'default_picking_id': self.id},
            'target': 'new',
        }
    
    @api.model
    def get_picking_statistics(self):
        """Get picking performance statistics"""
        domain = [('state', '=', 'done')]
        pickings = self.search(domain)
        
        total_pickings = len(pickings)
        avg_efficiency = sum(pickings.mapped('pick_efficiency')) / total_pickings if total_pickings else 0
        avg_pick_time = sum(pickings.mapped('total_pick_time')) / total_pickings if total_pickings else 0
        
        return {
            'total_pickings': total_pickings,
            'average_efficiency': avg_efficiency,
            'average_pick_time': avg_pick_time,
        }