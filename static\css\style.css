/* Custom styles for Odoo 18 Module Analyzer */

:root {
    --primary-color: #714B67;
    --secondary-color: #8A2387;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

/* Base styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Custom navbar */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Progress bars */
.progress {
    height: 0.5rem;
    border-radius: 0.375rem;
}

/* Badges */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* File upload area */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.file-upload-area:hover {
    border-color: #adb5bd;
    background-color: rgba(0, 0, 0, 0.025);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(113, 75, 103, 0.1);
}

/* Status indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-active {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

.status-pending {
    background-color: var(--warning-color);
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
}

.status-error {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

/* Compatibility score */
.compatibility-score {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.compatibility-score .progress {
    flex: 1;
    height: 8px;
}

/* Module structure badges */
.structure-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.structure-badges .badge {
    font-size: 0.7em;
    padding: 0.25em 0.5em;
}

/* File structure tree */
.file-tree {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.file-tree .folder {
    color: #ffc107;
}

.file-tree .file {
    color: #6c757d;
}

.file-tree .python-file {
    color: #28a745;
}

.file-tree .xml-file {
    color: #17a2b8;
}

.file-tree .js-file {
    color: #ffc107;
}

.file-tree .css-file {
    color: #007bff;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .display-4,
    .display-5,
    .display-6 {
        font-size: 2rem;
    }
    
    .lead {
        font-size: 1rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
    .file-upload-area {
        border-color: #495057;
        background-color: rgba(255, 255, 255, 0.025);
    }
    
    .file-upload-area:hover {
        border-color: #6c757d;
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* Animation for progress bars */
@keyframes progress-bar-stripes {
    0% {
        background-position-x: 1rem;
    }
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

/* Custom scrollbar for code blocks */
pre {
    max-height: 300px;
    overflow-y: auto;
}

pre::-webkit-scrollbar {
    width: 8px;
}

pre::-webkit-scrollbar-track {
    background: #f1f1f1;
}

pre::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

pre::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Accordion customizations */
.accordion-button:not(.collapsed) {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border-color: rgba(var(--bs-primary-rgb), 0.2);
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1055;
}
