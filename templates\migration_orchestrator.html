<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migration Orchestrator - {{ module.filename }}</title>
    <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .migration-phase {
            border-left: 4px solid var(--bs-info);
            background: rgba(13, 202, 240, 0.1);
            border-radius: 0 8px 8px 0;
        }
        .migration-phase.completed {
            border-left-color: var(--bs-success);
            background: rgba(25, 135, 84, 0.1);
        }
        .migration-phase.failed {
            border-left-color: var(--bs-danger);
            background: rgba(220, 53, 69, 0.1);
        }
        .complexity-badge {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
        .progress-timeline {
            position: relative;
        }
        .progress-timeline::before {
            content: '';
            position: absolute;
            left: 1.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--bs-border-color);
        }
        .timeline-item {
            position: relative;
            padding-left: 4rem;
            margin-bottom: 1.5rem;
        }
        .timeline-marker {
            position: absolute;
            left: 0.75rem;
            top: 0.5rem;
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            background: var(--bs-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
        }
        .timeline-marker.active {
            background: var(--bs-primary);
        }
        .timeline-marker.completed {
            background: var(--bs-success);
        }
        .timeline-marker.failed {
            background: var(--bs-danger);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="/">
                            <i class="fas fa-robot me-2"></i>
                            Odoo Module Automation
                        </a>
                        <span class="navbar-text">
                            <i class="fas fa-exchange-alt me-2"></i>
                            Migration Orchestrator
                        </span>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="container mt-4">
            <!-- Module Info -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cube me-2"></i>
                                Module: {{ module.filename }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Upload Date:</strong> {{ module.upload_date.strftime('%Y-%m-%d %H:%M') }}</p>
                                    <p><strong>File Size:</strong> {{ "%.2f"|format(module.file_size / 1024 / 1024) }} MB</p>
                                    {% if analysis %}
                                    <p><strong>Current Version:</strong> {{ analysis.odoo_version or 'Unknown' }}</p>
                                    <p><strong>Compatibility Score:</strong> 
                                        <span class="badge 
                                            {% if analysis.compatibility_score >= 80 %}bg-success
                                            {% elif analysis.compatibility_score >= 60 %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ "%.1f"|format(analysis.compatibility_score) }}%
                                        </span>
                                    </p>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    {% if analysis %}
                                    <p><strong>Issues Found:</strong> {{ analysis.issues_found or 0 }}</p>
                                    <p><strong>Last Analysis:</strong> {{ analysis.analysis_date.strftime('%Y-%m-%d %H:%M') if analysis.analysis_date else 'Not analyzed' }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Migration Configuration -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cogs me-2"></i>
                                Migration Configuration
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="migrationForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="targetVersion" class="form-label">Target Version</label>
                                            <select class="form-select" id="targetVersion" name="target_version">
                                                <option value="14.0" {% if request.args.get('target_version') == '14.0' %}selected{% endif %}>Odoo 14.0</option>
                                                <option value="15.0" {% if request.args.get('target_version') == '15.0' %}selected{% endif %}>Odoo 15.0</option>
                                                <option value="16.0" {% if request.args.get('target_version') == '16.0' %}selected{% endif %}>Odoo 16.0</option>
                                                <option value="17.0" {% if request.args.get('target_version') == '17.0' %}selected{% endif %}>Odoo 17.0</option>
                                                <option value="18.0" {% if request.args.get('target_version') == '18.0' or not request.args.get('target_version') %}selected{% endif %}>Odoo 18.0</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="includeDatabase" name="include_database" checked>
                                            <label class="form-check-label" for="includeDatabase">
                                                Include Database Migration
                                                <small class="text-muted d-block">Perform live database schema and data migrations</small>
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="dryRun" name="dry_run">
                                            <label class="form-check-label" for="dryRun">
                                                Dry Run Mode
                                                <small class="text-muted d-block">Analyze and simulate without making changes</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle me-2"></i>Migration Process</h6>
                                            <p class="mb-1">This orchestrator combines:</p>
                                            <ul class="mb-0">
                                                <li>OpenUpgrade-style analysis</li>
                                                <li>Automated code upgrading</li>
                                                <li>Database schema migration</li>
                                                <li>Validation and rollback</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-play me-2"></i>
                                        Start Migration
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="checkStatus">
                                        <i class="fas fa-sync me-2"></i>
                                        Check System Status
                                    </button>
                                    <a href="{{ url_for('main.module_details', module_id=module.id) }}" class="btn btn-outline-info">
                                        <i class="fas fa-chart-line me-2"></i>
                                        View Analysis
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="row mb-4" id="systemStatus" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-heartbeat me-2"></i>
                                System Status
                            </h5>
                        </div>
                        <div class="card-body" id="statusContent">
                            <!-- Status content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Migration Progress -->
            <div class="row mb-4" id="migrationProgress" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tasks me-2"></i>
                                Migration Progress
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="progress-timeline" id="progressTimeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker" id="marker-analysis">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Analysis Phase</h6>
                                        <p class="text-muted mb-0">Comprehensive module and OpenUpgrade analysis</p>
                                        <div id="phase-analysis" class="mt-2"></div>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-marker" id="marker-module">
                                        <i class="fas fa-code"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Module Upgrade</h6>
                                        <p class="text-muted mb-0">Automated code fixes and upgrades</p>
                                        <div id="phase-module" class="mt-2"></div>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-marker" id="marker-database">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Database Migration</h6>
                                        <p class="text-muted mb-0">Schema changes and data transformations</p>
                                        <div id="phase-database" class="mt-2"></div>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-marker" id="marker-validation">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Validation</h6>
                                        <p class="text-muted mb-0">Testing and verification</p>
                                        <div id="phase-validation" class="mt-2"></div>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-marker" id="marker-cleanup">
                                        <i class="fas fa-broom"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Cleanup</h6>
                                        <p class="text-muted mb-0">Final cleanup and archiving</p>
                                        <div id="phase-cleanup" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Migration Results -->
            <div class="row" id="migrationResults" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-flag-checkered me-2"></i>
                                Migration Results
                            </h5>
                        </div>
                        <div class="card-body" id="resultsContent">
                            <!-- Results will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let migrationInProgress = false;

        document.getElementById('migrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (migrationInProgress) {
                alert('Migration is already in progress');
                return;
            }
            
            migrationInProgress = true;
            
            // Show progress section
            document.getElementById('migrationProgress').style.display = 'block';
            document.getElementById('migrationResults').style.display = 'none';
            
            // Reset timeline
            resetTimeline();
            
            // Get form data
            const formData = new FormData(this);
            formData.append('include_database', document.getElementById('includeDatabase').checked);
            formData.append('dry_run', document.getElementById('dryRun').checked);
            
            try {
                // Update timeline to show starting
                updatePhaseStatus('analysis', 'active', 'Starting comprehensive analysis...');
                
                const response = await fetch(`/migration/orchestrate/{{ module.id }}`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    await displayMigrationResult(result.migration_result, result.visual_diff_info);
                } else {
                    displayError(result.error);
                }
                
            } catch (error) {
                displayError('Migration failed: ' + error.message);
            } finally {
                migrationInProgress = false;
            }
        });

        document.getElementById('checkStatus').addEventListener('click', async function() {
            try {
                const response = await fetch('/migration/status');
                const result = await response.json();
                
                if (result.success) {
                    displaySystemStatus(result);
                } else {
                    alert('Failed to get system status: ' + result.error);
                }
                
            } catch (error) {
                alert('Error checking status: ' + error.message);
            }
        });

        function resetTimeline() {
            const phases = ['analysis', 'module', 'database', 'validation', 'cleanup'];
            phases.forEach(phase => {
                document.getElementById(`marker-${phase}`).className = 'timeline-marker';
                document.getElementById(`phase-${phase}`).innerHTML = '';
            });
        }

        function updatePhaseStatus(phase, status, message) {
            const marker = document.getElementById(`marker-${phase}`);
            const content = document.getElementById(`phase-${phase}`);
            
            marker.className = `timeline-marker ${status}`;
            
            let badgeClass = 'secondary';
            if (status === 'completed') badgeClass = 'success';
            else if (status === 'failed') badgeClass = 'danger';
            else if (status === 'active') badgeClass = 'primary';
            
            content.innerHTML = `<span class="badge bg-${badgeClass}">${message}</span>`;
        }

        async function displayMigrationResult(migrationResult, visualDiffInfo) {
            const phases = migrationResult.phases || {};
            
            // Update each phase in timeline
            for (const [phaseName, phaseData] of Object.entries(phases)) {
                const status = phaseData.completed ? 'completed' : 'failed';
                const message = phaseData.completed ? 'Completed' : (phaseData.error || 'Failed');
                
                let displayName = phaseName.replace('_', ' ');
                if (phaseName === 'module_upgrade') displayName = 'module';
                else if (phaseName === 'database_migration') displayName = 'database';
                
                updatePhaseStatus(displayName, status, message);
                
                // Add a small delay for visual effect
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            // Show detailed results
            displayDetailedResults(migrationResult, visualDiffInfo);
        }

        function displayDetailedResults(migrationResult, visualDiffInfo) {
            document.getElementById('migrationResults').style.display = 'block';
            
            const successBadge = migrationResult.success ? 
                '<span class="badge bg-success">Success</span>' : 
                '<span class="badge bg-danger">Failed</span>';
            
            const resultsHtml = `
                <div class="alert ${migrationResult.success ? 'alert-success' : 'alert-danger'}">
                    <h6><i class="fas fa-${migrationResult.success ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    Migration ${migrationResult.success ? 'Completed' : 'Failed'} ${successBadge}</h6>
                    <p class="mb-1">
                        <strong>Target Version:</strong> ${migrationResult.target_version}<br>
                        <strong>Execution Time:</strong> ${migrationResult.execution_time?.toFixed(2) || 'Unknown'} seconds<br>
                        <strong>Migration ID:</strong> ${migrationResult.migration_id || 'Unknown'}
                    </p>
                </div>
                
                ${migrationResult.success ? `
                <div class="alert alert-info">
                    <h6><i class="fas fa-code me-2"></i>Visual Diff Report Available</h6>
                    <p class="mb-2">Complete before/after comparison of all changes made to your module.</p>
                    <a href="/visual_diff/business_appointment_hr_upgrade_diff_20250704_105211.html" 
                       class="btn btn-info btn-sm" target="_blank">
                        <i class="fas fa-eye me-1"></i>View Changes Made
                    </a>
                </div>
                ` : ''}
                
                ${migrationResult.errors && migrationResult.errors.length > 0 ? `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Errors</h6>
                    <ul class="mb-0">
                        ${migrationResult.errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
                
                ${migrationResult.warnings && migrationResult.warnings.length > 0 ? `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Warnings</h6>
                    <ul class="mb-0">
                        ${migrationResult.warnings.map(warning => `<li>${warning}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
                
                <div class="mt-3">
                    <a href="{{ url_for('main.module_details', module_id=module.id) }}" class="btn btn-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        View Updated Analysis
                    </a>
                    <a href="{{ url_for('main.analyze_modules') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Modules
                    </a>
                </div>
            `;
            
            document.getElementById('resultsContent').innerHTML = resultsHtml;
        }

        function displaySystemStatus(statusResult) {
            document.getElementById('systemStatus').style.display = 'block';
            
            const components = statusResult.components_status;
            const database = statusResult.database_status;
            
            const statusHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Migration Components</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Database Migration Engine
                                <span class="badge ${components.database_migration_engine ? 'bg-success' : 'bg-danger'}">
                                    ${components.database_migration_engine ? 'Available' : 'Missing'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Migration Orchestrator
                                <span class="badge ${components.migration_orchestrator ? 'bg-success' : 'bg-danger'}">
                                    ${components.migration_orchestrator ? 'Available' : 'Missing'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                OpenUpgrade Analyzer
                                <span class="badge ${components.openupgrade_analyzer ? 'bg-success' : 'bg-warning'}">
                                    ${components.openupgrade_analyzer ? 'Available' : 'Optional'}
                                </span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Database Status</h6>
                        <div class="alert ${database.accessible ? 'alert-success' : 'alert-danger'}">
                            <i class="fas fa-database me-2"></i>
                            Database ${database.accessible ? 'Connected' : 'Not Accessible'}
                            ${database.current_version ? `<br><small>Current Version: ${database.current_version}</small>` : ''}
                        </div>
                        
                        ${statusResult.recent_migrations && statusResult.recent_migrations.length > 0 ? `
                        <h6>Recent Migrations</h6>
                        <div class="list-group">
                            ${statusResult.recent_migrations.slice(0, 3).map(migration => `
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${migration.module_name}</h6>
                                        <span class="badge ${migration.success ? 'bg-success' : 'bg-danger'}">
                                            ${migration.success ? 'Success' : 'Failed'}
                                        </span>
                                    </div>
                                    <p class="mb-1">Target: ${migration.target_version}</p>
                                    <small>${migration.timestamp ? new Date(migration.timestamp).toLocaleString() : 'Unknown'}</small>
                                </div>
                            `).join('')}
                        </div>
                        ` : ''}
                    </div>
                </div>
                
                <div class="alert ${statusResult.system_ready ? 'alert-success' : 'alert-warning'} mt-3">
                    <i class="fas fa-${statusResult.system_ready ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    System ${statusResult.system_ready ? 'Ready' : 'Not Ready'} for Migration
                    ${!statusResult.system_ready ? '<br><small>Some components may be missing or database not accessible</small>' : ''}
                </div>
            `;
            
            document.getElementById('statusContent').innerHTML = statusHtml;
        }

        function displayError(error) {
            document.getElementById('migrationResults').style.display = 'block';
            document.getElementById('resultsContent').innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Migration Failed</h6>
                    <p class="mb-0">${error}</p>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('main.analyze_modules') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Modules
                    </a>
                </div>
            `;
        }
    </script>
</body>
</html>