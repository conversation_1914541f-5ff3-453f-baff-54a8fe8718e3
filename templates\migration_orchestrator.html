{% extends "base.html" %}
{% set title = "Migration Orchestrator" %}

{% block content %}
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-cogs me-2"></i>
                        Migration Orchestrator
                    </h2>
                    <p class="text-muted">Central hub for all migration types - Choose your migration strategy</p>
                </div>
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-primary" onclick="refreshStatus()" title="Refresh Status">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                    <a href="{{ url_for('main.migration_jobs') }}" class="btn btn-outline-info" title="View All Jobs">
                        <i class="fas fa-tasks"></i> View Jobs
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Migration Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-cubes fa-2x text-primary mb-2"></i>
                    <h4 class="card-title">{{ stats.total_modules }}</h4>
                    <p class="card-text">Total Modules</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="card-title">{{ stats.pending_jobs }}</h4>
                    <p class="card-text">Pending Jobs</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="card-title">{{ stats.completed_jobs }}</h4>
                    <p class="card-text">Completed</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                    <h4 class="card-title">{{ "%.1f"|format(stats.success_rate) }}%</h4>
                    <p class="card-text">Success Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Migration Type Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-route me-2"></i>
                        Choose Your Migration Type
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Single Module Migration -->
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-cube fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title">Single Module</h5>
                                    <p class="card-text">Migrate individual modules with full TrueMigrationOrchestrator analysis</p>
                                    <ul class="list-unstyled text-start small">
                                        <li><i class="fas fa-check text-success me-1"></i>AST-based code transformation</li>
                                        <li><i class="fas fa-check text-success me-1"></i>AI-powered analysis</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Visual diff review</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Automated testing</li>
                                    </ul>
                                    <a href="{{ url_for('main.analyze_modules') }}" class="btn btn-primary">
                                        <i class="fas fa-arrow-right me-1"></i>Start Single Migration
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Database Migration -->
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-database fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">Database Migration</h5>
                                    <p class="card-text">Full database schema and data migration with OpenUpgrade</p>
                                    <ul class="list-unstyled text-start small">
                                        <li><i class="fas fa-check text-success me-1"></i>OpenUpgrade integration</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Schema evolution</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Data preservation</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Rollback capability</li>
                                    </ul>
                                    <a href="{{ url_for('main.migration_jobs') }}" class="btn btn-success">
                                        <i class="fas fa-arrow-right me-1"></i>Database Migration
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Bulk Migration -->
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-layer-group fa-3x text-warning mb-3"></i>
                                    <h5 class="card-title">Bulk Migration</h5>
                                    <p class="card-text">Process multiple modules simultaneously with batch orchestration</p>
                                    <ul class="list-unstyled text-start small">
                                        <li><i class="fas fa-check text-success me-1"></i>Batch processing</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Dependency resolution</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Progress tracking</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Error handling</li>
                                    </ul>
                                    <a href="{{ url_for('main.bulk_migration') }}" class="btn btn-warning">
                                        <i class="fas fa-arrow-right me-1"></i>Bulk Migration
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Automated Migration -->
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-robot fa-3x text-info mb-3"></i>
                                    <h5 class="card-title">Automated Migration</h5>
                                    <p class="card-text">Fully automated migration cycles with AI-driven decisions</p>
                                    <ul class="list-unstyled text-start small">
                                        <li><i class="fas fa-check text-success me-1"></i>Scheduled automation</li>
                                        <li><i class="fas fa-check text-success me-1"></i>AI decision making</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Continuous integration</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Smart monitoring</li>
                                    </ul>
                                    <a href="{{ url_for('automation.automation_dashboard') }}" class="btn btn-info">
                                        <i class="fas fa-arrow-right me-1"></i>Automation Hub
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Migration Activity -->
    {% if recent_jobs %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Migration Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Module</th>
                                    <th>Target Version</th>
                                    <th>Status</th>
                                    <th>Started</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for job in recent_jobs %}
                                <tr>
                                    <td><strong>{{ job.module.name }}</strong></td>
                                    <td><span class="badge bg-secondary">v{{ job.target_version }}</span></td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if job.status == 'COMPLETED' else 'warning' if job.status in ['QUEUED', 'ANALYSIS'] else 'danger' if job.status == 'FAILED' else 'info' }}">
                                            {{ job.status.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>{{ job.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <a href="{{ url_for('main.migration_jobs') }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function refreshStatus() {
    location.reload();
}
</script>
{% endblock %}
