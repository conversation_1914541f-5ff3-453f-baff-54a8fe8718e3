<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Provider Configuration - Odoo Module Analysis</title>
    <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-cogs me-2"></i>Odoo Module Analysis
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('main.index') }}">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-robot me-2"></i>AI Provider Configuration</h1>
                    <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>

                <!-- Current Provider Status -->
                {% if current_provider and 'active_provider' in current_provider %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-check-circle text-success me-2"></i>Current AI Provider</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Provider:</strong> {{ current_provider.active_provider }}<br>
                                <strong>Model:</strong> {{ current_provider.model }}<br>
                                <strong>Cost:</strong> ${{ "%.3f"|format(current_provider.cost_per_1m_tokens) }} per 1M tokens
                            </div>
                            <div class="col-md-6">
                                <strong>Free Tier:</strong> 
                                {% if current_provider.free_tier %}
                                    <span class="badge bg-success">Yes</span>
                                {% else %}
                                    <span class="badge bg-warning">No</span>
                                {% endif %}<br>
                                <strong>Features:</strong><br>
                                {% for feature in current_provider.features %}
                                    <span class="badge bg-primary me-1">{{ feature }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Provider Selection -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Available AI Providers</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">
                            Choose your preferred AI provider based on cost, features, and availability. 
                            Free options are available including DeepSeek and OpenRouter.
                        </p>
                        
                        <div class="row">
                            {% for provider in providers %}
                            <div class="col-lg-6 col-xl-4 mb-3">
                                <div class="card h-100 {% if not provider.available %}border-secondary{% elif provider.free_tier %}border-success{% else %}border-primary{% endif %}">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <strong>{{ provider.name }}</strong>
                                        {% if provider.free_tier %}
                                            <span class="badge bg-success">FREE</span>
                                        {% else %}
                                            <span class="badge bg-info">PAID</span>
                                        {% endif %}
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text small">{{ provider.description }}</p>
                                        
                                        <div class="mb-2">
                                            <strong>Cost:</strong> 
                                            {% if provider.cost_per_1m_tokens == 0 %}
                                                <span class="text-success">FREE</span>
                                            {% else %}
                                                ${{ "%.3f"|format(provider.cost_per_1m_tokens) }}/1M tokens
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <strong>Features:</strong><br>
                                            {% for feature in provider.features %}
                                                <span class="badge bg-secondary me-1 mb-1">{{ feature }}</span>
                                            {% endfor %}
                                        </div>
                                        
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                Status: 
                                                {% if provider.available %}
                                                    <span class="text-success">✓ Ready</span>
                                                {% else %}
                                                    <span class="text-warning">{{ provider.status }}</span>
                                                {% endif %}
                                            </small>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        {% if provider.available %}
                                            <button class="btn btn-primary btn-sm w-100" 
                                                    onclick="setProvider('{{ provider.type }}')">
                                                <i class="fas fa-check me-1"></i>Use This Provider
                                            </button>
                                        {% else %}
                                            <small class="text-muted">
                                                {% if 'API key' in provider.status %}
                                                    <i class="fas fa-key me-1"></i>API key required
                                                {% elif 'not running' in provider.status %}
                                                    <i class="fas fa-server me-1"></i>Service not running
                                                {% else %}
                                                    <i class="fas fa-exclamation-triangle me-1"></i>Not available
                                                {% endif %}
                                            </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Setup Instructions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Setup Instructions</h5>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="setupAccordion">
                            <!-- DeepSeek Setup -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#deepseekSetup">
                                        <i class="fas fa-brain me-2"></i>DeepSeek (Recommended - 90% cheaper than GPT-4)
                                    </button>
                                </h2>
                                <div id="deepseekSetup" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>Visit <a href="https://platform.deepseek.com" target="_blank">platform.deepseek.com</a></li>
                                            <li>Create account and get API key</li>
                                            <li>Add environment variable: <code>DEEPSEEK_API_KEY=your_key_here</code></li>
                                            <li>Restart the application</li>
                                        </ol>
                                        <div class="alert alert-success">
                                            <strong>Free Trial:</strong> DeepSeek offers free credits for testing
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- OpenRouter Setup -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#openrouterSetup">
                                        <i class="fas fa-route me-2"></i>OpenRouter (Free tier available)
                                    </button>
                                </h2>
                                <div id="openrouterSetup" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>Visit <a href="https://openrouter.ai" target="_blank">openrouter.ai</a></li>
                                            <li>Create account and get API key</li>
                                            <li>Add environment variable: <code>OPENROUTER_API_KEY=your_key_here</code></li>
                                            <li>Restart the application</li>
                                        </ol>
                                        <div class="alert alert-info">
                                            <strong>Free Access:</strong> Provides free access to multiple models including DeepSeek
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- OpenAI Setup -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#openaiSetup">
                                        <i class="fas fa-robot me-2"></i>OpenAI (Paid but most reliable)
                                    </button>
                                </h2>
                                <div id="openaiSetup" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>Visit <a href="https://platform.openai.com" target="_blank">platform.openai.com</a></li>
                                            <li>Create account and add payment method</li>
                                            <li>Generate API key</li>
                                            <li>Add environment variable: <code>OPENAI_API_KEY=your_key_here</code></li>
                                            <li>Restart the application</li>
                                        </ol>
                                        <div class="alert alert-warning">
                                            <strong>Paid Service:</strong> Requires payment for usage
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Ollama Setup -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#ollamaSetup">
                                        <i class="fas fa-home me-2"></i>Ollama (Completely free local models)
                                    </button>
                                </h2>
                                <div id="ollamaSetup" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>Install Ollama: <code>curl -fsSL https://ollama.com/install.sh | sh</code></li>
                                            <li>Start Ollama: <code>ollama serve</code></li>
                                            <li>Download a model: <code>ollama pull llama3.2</code></li>
                                            <li>No API key required - runs completely locally</li>
                                        </ol>
                                        <div class="alert alert-success">
                                            <strong>Free & Private:</strong> No API keys, no internet required, complete privacy
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast for notifications -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">AI Provider</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toast-body">
                <!-- Message will be inserted here -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function setProvider(providerType) {
            fetch('/ai_providers/set', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    provider: providerType
                })
            })
            .then(response => response.json())
            .then(data => {
                const toast = document.getElementById('notification-toast');
                const toastBody = document.getElementById('toast-body');
                
                if (data.success) {
                    toastBody.textContent = data.message;
                    toastBody.className = 'toast-body text-success';
                    
                    // Reload page after short delay to show updated status
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    toastBody.textContent = data.message;
                    toastBody.className = 'toast-body text-danger';
                }
                
                new bootstrap.Toast(toast).show();
            })
            .catch(error => {
                console.error('Error:', error);
                const toast = document.getElementById('notification-toast');
                const toastBody = document.getElementById('toast-body');
                toastBody.textContent = 'Error setting AI provider';
                toastBody.className = 'toast-body text-danger';
                new bootstrap.Toast(toast).show();
            });
        }
    </script>
</body>
</html>