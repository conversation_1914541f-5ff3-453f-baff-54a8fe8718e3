Metadata-Version: 2.1
Name: bandit
Version: 1.7.5
Summary: Security oriented static analyser for python code.
Home-page: https://bandit.readthedocs.io/
Author: PyCQA
Author-email: <EMAIL>
License: Apache-2.0 license
Project-URL: Release Notes, https://github.com/PyCQA/bandit/releases
Project-URL: Source Code, https://github.com/PyCQA/bandit
Project-URL: Issue Tracker, https://github.com/PyCQA/bandit/issues
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: System Administrators
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Security
Requires-Python: >=3.7
Requires-Dist: GitPython (>=1.0.1)
Requires-Dist: PyYAML (>=5.3.1)
Requires-Dist: stevedore (>=1.20.0)
Requires-Dist: rich
Requires-Dist: colorama (>=0.3.9) ; (platform_system=="Windows")
Provides-Extra: test
Requires-Dist: coverage (>=4.5.4) ; extra == 'test'
Requires-Dist: fixtures (>=3.0.0) ; extra == 'test'
Requires-Dist: flake8 (>=4.0.0) ; extra == 'test'
Requires-Dist: stestr (>=2.5.0) ; extra == 'test'
Requires-Dist: testscenarios (>=0.5.0) ; extra == 'test'
Requires-Dist: testtools (>=2.3.0) ; extra == 'test'
Requires-Dist: beautifulsoup4 (>=4.8.0) ; extra == 'test'
Requires-Dist: pylint (==1.9.4) ; extra == 'test'
Requires-Dist: tomli (>=1.1.0) ; ((python_version<"3.11")) and extra == 'test'
Provides-Extra: toml
Requires-Dist: tomli (>=1.1.0) ; (python_version < "3.11") and extra == 'toml'
Provides-Extra: yaml
Requires-Dist: PyYAML ; extra == 'yaml'

.. image:: https://raw.githubusercontent.com/pycqa/bandit/main/logo/logotype-sm.png
    :alt: Bandit

======

.. image:: https://github.com/PyCQA/bandit/actions/workflows/pythonpackage.yml/badge.svg?branch=main
    :target: https://github.com/PyCQA/bandit/actions?query=workflow%3A%22Build+and+Test+Bandit%22+branch%3Amain
    :alt: Build Status

.. image:: https://readthedocs.org/projects/bandit/badge/?version=latest
    :target: https://readthedocs.org/projects/bandit/
    :alt: Docs Status

.. image:: https://img.shields.io/pypi/v/bandit.svg
    :target: https://pypi.org/project/bandit/
    :alt: Latest Version

.. image:: https://img.shields.io/pypi/pyversions/bandit.svg
    :target: https://pypi.org/project/bandit/
    :alt: Python Versions

.. image:: https://img.shields.io/pypi/format/bandit.svg
    :target: https://pypi.org/project/bandit/
    :alt: Format

.. image:: https://img.shields.io/badge/license-Apache%202-blue.svg
    :target: https://github.com/PyCQA/bandit/blob/main/LICENSE
    :alt: License

.. image:: https://img.shields.io/discord/825463413634891776.svg
    :target: https://discord.gg/qYxpadCgkx
    :alt: Discord

A security linter from PyCQA

* Free software: Apache license
* Documentation: https://bandit.readthedocs.io/en/latest/
* Source: https://github.com/PyCQA/bandit
* Bugs: https://github.com/PyCQA/bandit/issues
* Contributing: https://github.com/PyCQA/bandit/blob/main/CONTRIBUTING.md

Overview
--------

Bandit is a tool designed to find common security issues in Python code. To do
this Bandit processes each file, builds an AST from it, and runs appropriate
plugins against the AST nodes. Once Bandit has finished scanning all the files
it generates a report.

Bandit was originally developed within the OpenStack Security Project and
later rehomed to PyCQA.

.. image:: https://raw.githubusercontent.com/pycqa/bandit/main/bandit-terminal.png
    :alt: Bandit Example Screen Shot

Show Your Style
---------------

.. image:: https://img.shields.io/badge/security-bandit-yellow.svg
    :target: https://github.com/PyCQA/bandit
    :alt: Security Status

Use our badge in your project's README!

using Markdown::

    [![security: bandit](https://img.shields.io/badge/security-bandit-yellow.svg)](https://github.com/PyCQA/bandit)

using RST::

    .. image:: https://img.shields.io/badge/security-bandit-yellow.svg
        :target: https://github.com/PyCQA/bandit
        :alt: Security Status

References
----------

Python AST module documentation: https://docs.python.org/3/library/ast.html

Green Tree Snakes - the missing Python AST docs:
https://greentreesnakes.readthedocs.org/en/latest/

Documentation of the various types of AST nodes that Bandit currently covers
or could be extended to cover:
https://greentreesnakes.readthedocs.org/en/latest/nodes.html



