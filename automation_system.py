import requests
import time
import logging
import os
from github_module_puller import GitHubModulePuller
from models import db, OdooModule

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AutomationSystem:
    def __init__(self, config):
        self.config = config
        self.github_puller = GitHubModulePuller(
            repo_url=config['github_repo_url'],
            token=os.environ.get('GITHUB_TOKEN')
        )
        # The base URL for the web application's API
        self.api_base_url = "http://localhost:5000"

    def run_cycle(self, app_context=None):
        logging.info("Starting new automation cycle...")
        # In a production environment, target_versions would likely come from the config
        target_versions = ["17.0", "18.0"]

        # If no app context provided, skip database operations for now
        if app_context:
            try:
                # 1. Sync modules from GitHub
                new_modules = self.github_puller.pull_new_modules()
                logging.info(f"Pulled {len(new_modules)} new modules from GitHub.")

                # 2. Ensure modules are in our database
                for module_path in new_modules:
                    # This part would need a robust way to add/update modules
                    # in the DB, similar to the /upload route logic. For simplicity,
                    # we assume the upload process is the primary way to register them.
                    pass

                # 3. Find modules that haven't been migrated for the target versions
                all_modules = OdooModule.query.all()
                for module in all_modules:
                    for version in target_versions:
                        # Check if a successful migration job already exists for this version
                        existing_job = any(
                            job.target_version == version and job.status == 'COMPLETED'
                            for job in module.migration_jobs
                        )

                        if not existing_job:
                            logging.info(f"Queuing migration for module '{module.name}' to version {version}.")
                            self._trigger_migration(module.id, version)
                            time.sleep(2) # Stagger API requests slightly

            except Exception as e:
                logging.error(f"An error occurred during the automation cycle: {e}", exc_info=True)
        
        logging.info("Automation cycle finished.")

    def _trigger_migration(self, module_id, target_version):
        """
        Makes a reliable API call to the new, unified migration endpoint.
        """
        endpoint = f"{self.api_base_url}/migration_jobs"
        payload = {
            'module_id': module_id,
            'target_version': target_version
        }
        try:
            response = requests.post(endpoint, data=payload)
            if response.status_code == 302 or response.status_code == 200:
                logging.info(f"Successfully queued job for module ID {module_id} to v{target_version}.")
            else:
                logging.error(f"Failed to queue job for module ID {module_id}. Status: {response.status_code}, Response: {response.text}")
        except requests.exceptions.RequestException as e:
            logging.error(f"API call to queue job for module ID {module_id} failed: {e}")

if __name__ == '__main__':
    # This would be run by a scheduler (like the hourly_scheduler.py or a cron job)
    # The config would be loaded from automation_config.json
    import json
    with open('automation_config.json', 'r') as f:
        config = json.load(f)
    
    automation_system = AutomationSystem(config)
    automation_system.run_cycle()