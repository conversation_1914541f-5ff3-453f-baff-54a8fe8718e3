{
    'name': 'Legacy Sales Extensions',
    'version': '********.0',
    'category': 'Sales',
    'summary': 'Legacy sales module with old API usage',
    'description': """
Legacy Sales Extensions
=======================

This module demonstrates old Odoo API patterns that need migration:
* Uses old API decorators (@api.one, @api.multi)
* Uses deprecated import patterns
* Contains outdated field definitions
* Requires migration for Odoo 18 compatibility
    """,
    'author': 'Legacy Developer',
    'website': 'http://legacy-odoo-modules.com',
    'depends': ['sale', 'account'],
    'data': [
        'views/sale_order_views.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
}