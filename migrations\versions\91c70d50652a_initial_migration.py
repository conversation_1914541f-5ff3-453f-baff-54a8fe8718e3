"""Initial migration.

Revision ID: 91c70d50652a
Revises: 
Create Date: 2025-07-05 16:35:44.868608

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '91c70d50652a'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('odoo_module',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('version', sa.String(length=32), nullable=True),
    sa.Column('path', sa.String(length=256), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('migration_job',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('module_id', sa.Integer(), nullable=False),
    sa.Column('target_version', sa.String(length=32), nullable=False),
    sa.Column('status', sa.String(length=64), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.Column('log', sa.Text(), nullable=True),
    sa.Column('upgraded_module_path', sa.String(length=256), nullable=True),
    sa.Column('security_report', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['module_id'], ['odoo_module.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('module_analysis',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('module_id', sa.Integer(), nullable=False),
    sa.Column('report', sa.JSON(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['module_id'], ['odoo_module.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('diff_report',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('migration_job_id', sa.Integer(), nullable=False),
    sa.Column('report_path', sa.String(length=256), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['migration_job_id'], ['migration_job.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('diff_report')
    op.drop_table('module_analysis')
    op.drop_table('migration_job')
    op.drop_table('odoo_module')
    # ### end Alembic commands ###
