{% extends "base.html" %}
{% set title = "Testing Dashboard" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-flask me-3"></i>
            Testing Dashboard
        </h1>
        <p class="lead">Comprehensive module testing with Docker, Runbot, and AI analysis</p>
    </div>
</div>

<!-- Testing Status Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card {{ 'border-success' if testing_available else 'border-warning' }}">
            <div class="card-body text-center">
                <i class="fas fa-{{ 'check-circle text-success' if testing_available else 'exclamation-triangle text-warning' }} fa-2x mb-2"></i>
                <h5 class="card-title">Testing Engine</h5>
                <span class="badge bg-{{ 'success' if testing_available else 'warning' }}">
                    {{ 'Available' if testing_available else 'Unavailable' }}
                </span>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card {{ 'border-info' if testing_config.docker_available else 'border-secondary' }}">
            <div class="card-body text-center">
                <i class="fab fa-docker fa-2x mb-2 {{ 'text-info' if testing_config.docker_available else 'text-secondary' }}"></i>
                <h5 class="card-title">Docker Testing</h5>
                <span class="badge bg-{{ 'info' if testing_config.docker_available else 'secondary' }}">
                    {{ 'Ready' if testing_config.docker_available else 'Not Available' }}
                </span>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card {{ 'border-warning' if testing_config.runbot_configured else 'border-secondary' }}">
            <div class="card-body text-center">
                <i class="fas fa-cloud fa-2x mb-2 {{ 'text-warning' if testing_config.runbot_configured else 'text-secondary' }}"></i>
                <h5 class="card-title">Runbot Testing</h5>
                <span class="badge bg-{{ 'warning' if testing_config.runbot_configured else 'secondary' }}">
                    {{ 'Configured' if testing_config.runbot_configured else 'Not Configured' }}
                </span>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card {{ 'border-primary' if testing_config.ai_available else 'border-secondary' }}">
            <div class="card-body text-center">
                <i class="fas fa-brain fa-2x mb-2 {{ 'text-primary' if testing_config.ai_available else 'text-secondary' }}"></i>
                <h5 class="card-title">AI Analysis</h5>
                <span class="badge bg-{{ 'primary' if testing_config.ai_available else 'secondary' }}">
                    {{ 'Ready' if testing_config.ai_available else 'Not Available' }}
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-play me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <a href="{{ url_for('testing.test_module') }}" class="btn btn-primary">
                        <i class="fas fa-vial me-1"></i>Test Module
                    </a>
                    <a href="{{ url_for('testing.testing_config') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-1"></i>Configuration
                    </a>
                    {% if not testing_available %}
                    <button class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#setupModal">
                        <i class="fas fa-wrench me-1"></i>Setup Testing
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feature Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Testing Capabilities</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fab fa-docker me-1"></i>Docker Testing</h6>
                        <ul class="small">
                            <li>Isolated environment testing</li>
                            <li>Multiple Odoo versions (v13-v18)</li>
                            <li>Automated installation validation</li>
                            <li>Error log capture and analysis</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-cloud me-1"></i>Runbot Integration</h6>
                        <ul class="small">
                            <li>Cloud-based testing platform</li>
                            <li>Real production environment</li>
                            <li>Comprehensive test suites</li>
                            <li>Performance benchmarking</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-brain me-1"></i>AI Analysis</h6>
                        <ul class="small">
                            <li>Intelligent error analysis</li>
                            <li>Automated fix suggestions</li>
                            <li>Code quality assessment</li>
                            <li>Migration recommendations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if recent_tests %}
<!-- Recent Test Results -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Test Results</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Test ID</th>
                                <th>Timestamp</th>
                                <th>Tests</th>
                                <th>Success Rate</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for test in recent_tests %}
                            <tr>
                                <td>
                                    <code>{{ test.test_id[:12] }}...</code>
                                </td>
                                <td>{{ test.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ test.total_tests }}</span>
                                    <small class="text-muted ms-1">
                                        ({{ test.passed }} passed, {{ test.failed }} failed)
                                    </small>
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-{{ 'success' if test.success_rate >= 80 else 'warning' if test.success_rate >= 60 else 'danger' }}" 
                                             style="width: {{ test.success_rate }}%">
                                            {{ test.success_rate|round(1) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if test.success_rate >= 80 else 'warning' if test.success_rate >= 60 else 'danger' }}">
                                        {{ 'Passed' if test.success_rate >= 80 else 'Issues' if test.success_rate >= 60 else 'Failed' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('testing.test_results', test_id=test.test_id) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('testing.download_test_report', test_id=test.test_id) }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<!-- No Tests Yet -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-flask fa-3x text-muted mb-3"></i>
                <h5>No Test Results Yet</h5>
                <p class="text-muted">Start testing modules to see results here.</p>
                <a href="{{ url_for('testing.test_module') }}" class="btn btn-primary">
                    <i class="fas fa-vial me-1"></i>Run Your First Test
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}