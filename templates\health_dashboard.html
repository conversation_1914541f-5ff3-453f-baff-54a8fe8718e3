
{% extends "base.html" %}
{% set title = "System Health Dashboard" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-heartbeat me-3"></i>
            System Health Dashboard
        </h1>
        <p class="lead">Monitor system components and resolve issues</p>
    </div>
</div>

<!-- Overall Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-{{ 'success' if health.overall_status == 'healthy' else 'warning' if health.overall_status == 'warning' else 'danger' }}">
            <div class="card-body text-center">
                <i class="fas fa-{{ 'check-circle text-success' if health.overall_status == 'healthy' else 'exclamation-triangle text-warning' if health.overall_status == 'warning' else 'times-circle text-danger' }} fa-3x mb-3"></i>
                <h3>System Status: {{ health.overall_status.title() }}</h3>
                <p class="text-muted">Last checked: {{ health.timestamp }}</p>
                {% if health.errors or health.warnings %}
                <button class="btn btn-primary" onclick="fixCommonIssues()">
                    <i class="fas fa-wrench me-1"></i>Auto-Fix Common Issues
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Service Status Cards -->
<div class="row mb-4">
    {% for service_name, service_data in health.services.items() %}
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="card border-{{ 'success' if service_data.status == 'healthy' else 'warning' if service_data.status == 'unavailable' else 'danger' }}">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{{ 'cog' if service_name == 'automation' else 'docker' if service_name == 'docker' else 'flask' if service_name == 'testing' else 'clock' if service_name == 'scheduler' else 'file-alt' }} me-2"></i>
                    {{ service_name.title() }}
                </h5>
            </div>
            <div class="card-body">
                <span class="badge bg-{{ 'success' if service_data.status == 'healthy' else 'warning' if service_data.status == 'unavailable' else 'danger' }} mb-2">
                    {{ service_data.status.title() }}
                </span>
                
                {% if service_data.get('error') %}
                <div class="alert alert-danger alert-sm">
                    <small>{{ service_data.error }}</small>
                </div>
                {% endif %}
                
                {% if service_name == 'docker' and service_data.get('available') %}
                <div class="text-success">
                    <i class="fas fa-check me-1"></i>Docker Available
                </div>
                {% endif %}
                
                {% if service_name == 'testing' %}
                <div class="mt-2">
                    <small class="text-muted">
                        Docker: {{ 'Enabled' if service_data.get('docker_enabled') else 'Disabled' }}<br>
                        AI: {{ 'Enabled' if service_data.get('ai_enabled') else 'Disabled' }}
                    </small>
                </div>
                {% endif %}
                
                {% if service_name == 'scheduler' %}
                <div class="mt-2">
                    <small class="text-muted">
                        {% if service_data.get('last_run') %}
                        Last Run: {{ service_data.last_run }}<br>
                        {% endif %}
                        {% if service_data.get('next_run') %}
                        Next Run: {{ service_data.next_run }}
                        {% endif %}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Errors and Warnings -->
{% if health.errors %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Errors ({{ health.errors|length }})
                </h5>
            </div>
            <div class="card-body">
                {% for error in health.errors %}
                <div class="alert alert-danger">
                    {{ error }}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

{% if health.warnings %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Warnings ({{ health.warnings|length }})
                </h5>
            </div>
            <div class="card-body">
                {% for warning in health.warnings %}
                <div class="alert alert-warning">
                    {{ warning }}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
function fixCommonIssues() {
    fetch('/health/fix-common-issues', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Fixes applied: ' + data.fixes_applied.join(', '));
            location.reload();
        } else {
            alert('Fix failed: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error applying fixes: ' + error);
    });
}

// Auto-refresh every 30 seconds
setTimeout(() => {
    location.reload();
}, 30000);
</script>
{% endblock %}
