{% extends "base.html" %}

{% block title %}Migration Jobs - Odoo Module Analysis Platform{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Migration Jobs</h2>
                <div>
                    <button class="btn btn-outline-secondary" onclick="refreshJobs()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Migration
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Total Jobs</h6>
                            <h3 class="mb-0" id="total-jobs">-</h3>
                        </div>
                        <i class="fas fa-tasks fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Completed</h6>
                            <h3 class="mb-0" id="completed-jobs">-</h3>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">In Progress</h6>
                            <h3 class="mb-0" id="running-jobs">-</h3>
                        </div>
                        <i class="fas fa-cog fa-spin fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Failed</h6>
                            <h3 class="mb-0" id="failed-jobs">-</h3>
                        </div>
                        <i class="fas fa-exclamation-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Jobs Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Migration Jobs</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="jobs-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Module</th>
                                    <th>Version</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                    <th>Phase</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="jobs-tbody">
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">Loading migration jobs...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Details Modal -->
<div class="modal fade" id="jobDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Job Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="job-details-content">
                <!-- Job details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="view-diff-btn" onclick="viewDiff()">
                    <i class="fas fa-eye"></i> View Diff
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentJobId = null;
let jobsData = [];

function refreshJobs() {
    loadMigrationJobs();
}

function loadMigrationJobs() {
    fetch('/api/migration-jobs')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                jobsData = data.jobs;
                updateJobsTable(data.jobs);
                updateJobStatistics(data.jobs);
            } else {
                console.error('Failed to load jobs:', data.error);
                showError('Failed to load migration jobs');
            }
        })
        .catch(error => {
            console.error('Error loading jobs:', error);
            showError('Error loading migration jobs');
        });
}

function updateJobsTable(jobs) {
    const tbody = document.getElementById('jobs-tbody');
    
    if (jobs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No migration jobs found</p>
                    <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-primary">
                        Create First Migration Job
                    </a>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = jobs.map(job => `
        <tr data-job-id="${job.id}">
            <td>${job.id}</td>
            <td>
                <strong>${job.module_name}</strong>
            </td>
            <td>
                <span class="badge bg-info">${job.source_version}</span>
                <i class="fas fa-arrow-right mx-1"></i>
                <span class="badge bg-success">${job.target_version}</span>
            </td>
            <td>
                <span class="badge ${getStatusBadgeClass(job.status)}">${job.status}</span>
            </td>
            <td>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar ${getProgressBarClass(job.status)}" 
                         role="progressbar" 
                         style="width: ${job.progress || 0}%"
                         aria-valuenow="${job.progress || 0}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        ${job.progress || 0}%
                    </div>
                </div>
            </td>
            <td>${job.current_phase || '-'}</td>
            <td>${formatDateTime(job.created_at)}</td>
            <td>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewJobDetails(${job.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="viewJobDiff(${job.id})" title="View Diff">
                        <i class="fas fa-code-branch"></i>
                    </button>
                    ${job.status !== 'COMPLETED' && job.status !== 'FAILED' && job.status !== 'CANCELLED' ? 
                        `<button class="btn btn-sm btn-outline-danger" onclick="cancelJob(${job.id})" title="Cancel Job">
                            <i class="fas fa-stop"></i>
                        </button>` : ''
                    }
                </div>
            </td>
        </tr>
    `).join('');
}

function updateJobStatistics(jobs) {
    const total = jobs.length;
    const completed = jobs.filter(j => j.status === 'COMPLETED').length;
    const running = jobs.filter(j => ['PYTHON_TRANSFORMATION', 'XML_TRANSFORMATION', 'VISUAL_DIFF', 'AI_ANALYSIS'].includes(j.status)).length;
    const failed = jobs.filter(j => j.status === 'FAILED').length;
    
    document.getElementById('total-jobs').textContent = total;
    document.getElementById('completed-jobs').textContent = completed;
    document.getElementById('running-jobs').textContent = running;
    document.getElementById('failed-jobs').textContent = failed;
}

function getStatusBadgeClass(status) {
    const statusClasses = {
        'UPLOADED': 'bg-secondary',
        'PYTHON_TRANSFORMATION': 'bg-warning',
        'XML_TRANSFORMATION': 'bg-warning',
        'VISUAL_DIFF': 'bg-warning',
        'AI_ANALYSIS': 'bg-warning',
        'MANUAL_INTERVENTION': 'bg-warning',
        'COMPLETED': 'bg-success',
        'FAILED': 'bg-danger',
        'CANCELLED': 'bg-secondary'
    };
    return statusClasses[status] || 'bg-secondary';
}

function getProgressBarClass(status) {
    const statusClasses = {
        'UPLOADED': 'bg-secondary',
        'PYTHON_TRANSFORMATION': 'bg-warning',
        'XML_TRANSFORMATION': 'bg-warning',
        'VISUAL_DIFF': 'bg-info',
        'AI_ANALYSIS': 'bg-info',
        'MANUAL_INTERVENTION': 'bg-warning',
        'COMPLETED': 'bg-success',
        'FAILED': 'bg-danger',
        'CANCELLED': 'bg-secondary'
    };
    return statusClasses[status] || 'bg-secondary';
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function viewJobDetails(jobId) {
    currentJobId = jobId;
    
    fetch(`/api/job-status/${jobId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showJobDetails(data.job);
            } else {
                showError('Failed to load job details');
            }
        })
        .catch(error => {
            console.error('Error loading job details:', error);
            showError('Error loading job details');
        });
}

function showJobDetails(job) {
    const content = document.getElementById('job-details-content');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>Job ID:</strong></td><td>${job.id}</td></tr>
                    <tr><td><strong>Module:</strong></td><td>${job.module_name}</td></tr>
                    <tr><td><strong>Source Version:</strong></td><td>${job.source_version}</td></tr>
                    <tr><td><strong>Target Version:</strong></td><td>${job.target_version}</td></tr>
                    <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusBadgeClass(job.status)}">${job.status}</span></td></tr>
                    <tr><td><strong>Progress:</strong></td><td>${job.progress_percentage || 0}%</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Timeline</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>Created:</strong></td><td>${formatDateTime(job.created_at)}</td></tr>
                    <tr><td><strong>Updated:</strong></td><td>${formatDateTime(job.updated_at)}</td></tr>
                    <tr><td><strong>Current Phase:</strong></td><td>${job.current_phase || '-'}</td></tr>
                    <tr><td><strong>Files Count:</strong></td><td>${job.files_count}</td></tr>
                    <tr><td><strong>Interventions:</strong></td><td>${job.interventions_count}</td></tr>
                </table>
            </div>
        </div>
        
        <!-- Week 4: Semantic Analysis Results -->
        ${job.semantic_analysis_data ? `
            <div class="mt-4">
                <h6>📊 Semantic Analysis Results <span class="badge bg-info">Week 4</span></h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-body">
                                <h6 class="card-title">Quality Metrics</h6>
                                <div class="mb-2">
                                    <small class="text-muted">Overall Quality Score</small>
                                    <div class="progress">
                                        <div class="progress-bar ${getQualityColor(job.semantic_analysis_data.overall_quality_score)}" 
                                             style="width: ${(job.semantic_analysis_data.overall_quality_score * 100).toFixed(0)}%">
                                            ${(job.semantic_analysis_data.overall_quality_score * 100).toFixed(1)}%
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Code Maintainability</small>
                                    <div class="progress">
                                        <div class="progress-bar bg-info" 
                                             style="width: ${(job.semantic_analysis_data.code_maintainability * 100).toFixed(0)}%">
                                            ${(job.semantic_analysis_data.code_maintainability * 100).toFixed(1)}%
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Business Logic Integrity</small>
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" 
                                             style="width: ${(job.semantic_analysis_data.business_logic_integrity * 100).toFixed(0)}%">
                                            ${(job.semantic_analysis_data.business_logic_integrity * 100).toFixed(1)}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-secondary">
                            <div class="card-body">
                                <h6 class="card-title">Analysis Summary</h6>
                                <p class="mb-2"><strong>Confidence:</strong> 
                                    <span class="badge ${getConfidenceBadge(job.semantic_analysis_data.confidence_level)}">
                                        ${job.semantic_analysis_data.confidence_level}
                                    </span>
                                </p>
                                <p class="mb-2"><strong>Issues Found:</strong> ${job.semantic_analysis_data.semantic_issues ? job.semantic_analysis_data.semantic_issues.length : 0}</p>
                                <p class="small text-muted">${job.semantic_analysis_data.analysis_summary || 'Analysis completed successfully'}</p>
                                ${job.semantic_analysis_data.semantic_issues && job.semantic_analysis_data.semantic_issues.length > 0 ? `
                                    <hr>
                                    <h6 class="small">Top Issues:</h6>
                                    ${job.semantic_analysis_data.semantic_issues.slice(0, 3).map(issue => `
                                        <div class="alert alert-${getSeverityColor(issue.severity)} alert-sm">
                                            <small><strong>${issue.type}:</strong> ${issue.description}</small>
                                        </div>
                                    `).join('')}
                                ` : ''}
                            </div>
                        </div>
                    </div>
                                </div>
            </div>
        ` : ''}
        
        ${job.error_message ? `
            <div class="alert alert-danger mt-3">
                <h6>Error Message</h6>
                <pre>${job.error_message}</pre>
            </div>
        ` : ''}
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('jobDetailsModal'));
    modal.show();
}

function viewJobDiff(jobId) {
    fetch(`/api/visual-diff/${jobId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.open(data.diff_url, '_blank');
            } else {
                showError('Failed to generate diff report');
            }
        })
        .catch(error => {
            console.error('Error generating diff:', error);
            showError('Error generating diff report');
        });
}

function viewDiff() {
    if (currentJobId) {
        viewJobDiff(currentJobId);
    }
}

function cancelJob(jobId) {
    if (!confirm('Are you sure you want to cancel this migration job?')) {
        return;
    }
    
    fetch(`/api/cancel-job/${jobId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Job cancelled successfully');
            loadMigrationJobs(); // Refresh the table
        } else {
            showError('Failed to cancel job: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error cancelling job:', error);
        showError('Error cancelling job');
    });
}

function showError(message) {
    // Simple error display - could be enhanced with toast notifications
    alert('Error: ' + message);
}

function showSuccess(message) {
    // Simple success display - could be enhanced with toast notifications
    alert('Success: ' + message);
}

// Auto-refresh jobs every 30 seconds
setInterval(loadMigrationJobs, 30000);

// Load jobs on page load
document.addEventListener('DOMContentLoaded', function() {
    loadMigrationJobs();
    
    // Check for highlight parameter
    const urlParams = new URLSearchParams(window.location.search);
    const highlightJobId = urlParams.get('highlight');
    if (highlightJobId) {
        setTimeout(() => highlightJob(highlightJobId), 1000);
    }
});

function highlightJob(jobId) {
    const row = document.querySelector(`tr[data-job-id="${jobId}"]`);
    if (row) {
        row.classList.add('table-warning');
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Remove highlight after 3 seconds
        setTimeout(() => {
            row.classList.remove('table-warning');
        }, 3000);
    }
}

// Week 4: Helper functions for semantic analysis display
function getQualityColor(score) {
    if (score >= 0.8) return 'bg-success';
    if (score >= 0.6) return 'bg-warning';
    return 'bg-danger';
}

function getConfidenceBadge(confidence) {
    switch (confidence?.toLowerCase()) {
        case 'high': return 'bg-success';
        case 'medium': return 'bg-warning';
        default: return 'bg-danger';
    }
}

function getSeverityColor(severity) {
    switch (severity?.toLowerCase()) {
        case 'low': return 'info';
        case 'medium': return 'warning';
        case 'high': return 'danger';
        case 'critical': return 'danger';
        default: return 'secondary';
    }
}
</script>
{% endblock %}