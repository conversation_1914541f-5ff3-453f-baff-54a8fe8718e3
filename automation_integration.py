"""
Integration module between the web application and automation system.
This module provides API endpoints and background services for automation.
"""

import os
import json
import threading
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path
from flask import Blueprint, request, jsonify, flash, redirect, url_for, render_template
from app import db
from models import OdooModule, ModuleAnalysis
from automation_system import OdooModuleAutomationSystem
from module_sync_manager import ModuleSyncManager
from hourly_scheduler import get_scheduler, start_scheduler

# Create blueprint for automation endpoints
automation_blueprint = Blueprint('automation', __name__, url_prefix='/automation')

# Setup logging
logger = logging.getLogger(__name__)

class AutomationService:
    """Service class for managing automation operations."""
    
    def __init__(self):
        self.automation_system = None
        self.sync_manager = ModuleSyncManager()
        self.scheduler = None
        self.is_running = False
        self.last_run = None
        self.next_run = None
        self.stats = {
            'total_processed': 0,
            'successful_upgrades': 0,
            'failed_upgrades': 0,
            'last_cycle_duration': 0
        }
    
    def initialize(self):
        """Initialize the automation system."""
        try:
            self.automation_system = OdooModuleAutomationSystem()
            return True
        except Exception as e:
            print(f"Failed to initialize automation system: {e}")
            return False
    
    def sync_web_modules_to_automation(self):
        """Sync modules from web application to automation system."""
        if not self.automation_system:
            return False
        
        try:
            # Get all analyzed modules from the web application
            modules = OdooModule.query.all()
            
            synced_count = 0
            for module in modules:
                analysis = ModuleAnalysis.query.filter_by(module_id=module.id).first()
                if analysis and analysis.analysis_data:
                    target_version = analysis.analysis_data.get('target_version', '18.0')
                    
                    # Determine appropriate folder based on compatibility score
                    compatibility_score = analysis.compatibility_score or 0
                    
                    if compatibility_score >= 95:
                        # High compatibility - can go to original folder
                        version_folder = f"v{target_version.split('.')[0]}_original"
                    else:
                        # Needs upgrading - put in appropriate original folder for processing
                        source_version = self._determine_source_version(analysis)
                        version_folder = f"v{source_version}_original"
                    
                    # Copy module to automation system
                    dest_path = Path(self.automation_system.config['directories']['base_path']) / version_folder
                    dest_path.mkdir(parents=True, exist_ok=True)
                    
                    dest_file = dest_path / module.filename
                    if not dest_file.exists():
                        import shutil
                        shutil.copy2(module.file_path, dest_file)
                        synced_count += 1
            
            return synced_count
        except Exception as e:
            print(f"Failed to sync modules: {e}")
            return 0
    
    def _determine_source_version(self, analysis):
        """Determine the source version of a module based on its characteristics."""
        # Analyze compatibility issues to determine likely source version
        issues = analysis.compatibility_issues or []
        warnings = analysis.compatibility_warnings or []
        
        # Count version-specific patterns
        v15_patterns = sum(1 for issue in issues if any(pattern in issue.lower() 
                          for pattern in ['@api.one', '@api.multi', 'openerp', 'size=']))
        v16_patterns = sum(1 for issue in issues if any(pattern in issue.lower() 
                          for pattern in ['legacy javascript', 'old xml patterns']))
        
        if v15_patterns > 0:
            return "15"
        elif v16_patterns > 0:
            return "16"
        else:
            return "17"  # Default to v17 if unclear
    
    def run_automation_cycle(self):
        """Run a single automation cycle."""
        if not self.automation_system or self.is_running:
            return False
        
        self.is_running = True
        start_time = datetime.now()
        
        try:
            # Sync web modules first
            synced = self.sync_web_modules_to_automation()
            print(f"Synced {synced} modules from web application")
            
            # Run automation cycle
            self.automation_system.run_automation_cycle()
            
            # Update stats
            end_time = datetime.now()
            self.last_run = end_time
            self.stats['last_cycle_duration'] = (end_time - start_time).total_seconds()
            
            return True
        except Exception as e:
            print(f"Automation cycle failed: {e}")
            return False
        finally:
            self.is_running = False
    
    def get_status(self):
        """Get current automation status."""
        return {
            'is_running': self.is_running,
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'next_run': self.next_run.isoformat() if self.next_run else None,
            'stats': self.stats,
            'system_initialized': self.automation_system is not None
        }

# Global automation service instance
automation_service = AutomationService()

@automation_blueprint.route('/')
def automation_dashboard():
    """Automation dashboard showing system status and controls."""
    status = automation_service.get_status()
    
    # Get automation system report if available
    report = None
    if automation_service.automation_system:
        try:
            report = automation_service.automation_system.generate_status_report()
        except:
            pass
    
    return render_template('automation_dashboard.html', 
                         status=status, 
                         report=report)

@automation_blueprint.route('/initialize', methods=['POST'])
def initialize_automation():
    """Initialize the automation system."""
    if automation_service.initialize():
        flash('Automation system initialized successfully', 'success')
    else:
        flash('Failed to initialize automation system', 'error')
    
    return redirect(url_for('automation.automation_dashboard'))

@automation_blueprint.route('/run_cycle', methods=['POST'])
def run_automation_cycle():
    """Trigger a single automation cycle."""
    if automation_service.is_running:
        flash('Automation cycle is already running', 'warning')
    else:
        # Run in background thread
        thread = threading.Thread(target=automation_service.run_automation_cycle)
        thread.daemon = True
        thread.start()
        flash('Automation cycle started', 'info')
    
    return redirect(url_for('automation.automation_dashboard'))

@automation_blueprint.route('/sync_modules', methods=['POST'])
def sync_modules():
    """Sync modules from web application to automation system."""
    if not automation_service.automation_system:
        flash('Automation system not initialized', 'error')
        return redirect(url_for('automation.automation_dashboard'))
    
    synced_count = automation_service.sync_web_modules_to_automation()
    flash(f'Synced {synced_count} modules to automation system', 'success')
    
    return redirect(url_for('automation.automation_dashboard'))

@automation_blueprint.route('/api/status')
def api_automation_status():
    """API endpoint for automation status."""
    return jsonify(automation_service.get_status())

@automation_blueprint.route('/api/trigger_cycle', methods=['POST'])
def api_trigger_cycle():
    """API endpoint to trigger automation cycle."""
    if automation_service.is_running:
        return jsonify({'success': False, 'message': 'Automation already running'})
    
    # Run in background thread
    thread = threading.Thread(target=automation_service.run_automation_cycle)
    thread.daemon = True
    thread.start()
    
    return jsonify({'success': True, 'message': 'Automation cycle started'})

@automation_blueprint.route('/config')
def automation_config():
    """Show and edit automation configuration."""
    config_path = 'automation_config.json'
    config = {}
    
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    
    return render_template('automation_config.html', config=config)

@automation_blueprint.route('/config', methods=['POST'])
def update_automation_config():
    """Update automation configuration."""
    config_path = 'automation_config.json'
    
    try:
        # Get configuration from form
        config = {
            'github': {
                'repo_url': request.form.get('github_repo_url', ''),
                'branch': request.form.get('github_branch', 'main'),
                'auto_commit': request.form.get('github_auto_commit') == 'on'
            },
            'processing': {
                'batch_size': int(request.form.get('batch_size', 5)),
                'delay_between_batches': int(request.form.get('delay_between_batches', 30)),
                'quality_threshold': float(request.form.get('quality_threshold', 85.0)),
                'auto_fix_enabled': request.form.get('auto_fix_enabled') == 'on',
                'advanced_upgrade_enabled': request.form.get('advanced_upgrade_enabled') == 'on'
            }
        }
        
        # Save configuration
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        flash('Configuration updated successfully', 'success')
        
        # Reinitialize automation system with new config
        automation_service.initialize()
        
    except Exception as e:
        flash(f'Failed to update configuration: {str(e)}', 'error')
    
    return redirect(url_for('automation.automation_config'))

@automation_blueprint.route('/logs')
def automation_logs():
    """Show automation logs and reports."""
    logs_dir = Path('odoo_modules/automation_logs')
    log_files = []
    
    if logs_dir.exists():
        for log_file in logs_dir.glob('*.log'):
            stat = log_file.stat()
            log_files.append({
                'name': log_file.name,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'path': str(log_file)
            })
    
    return render_template('automation_logs.html', log_files=log_files)

@automation_blueprint.route('/download_log/<path:filename>')
def download_log(filename):
    """Download a specific log file."""
    from flask import send_file
    log_path = Path('odoo_modules/automation_logs') / filename
    
    if log_path.exists() and log_path.is_file():
        return send_file(log_path, as_attachment=True)
    else:
        flash('Log file not found', 'error')
        return redirect(url_for('automation.automation_logs'))

# Background automation scheduler
class AutomationScheduler:
    """Background scheduler for automation cycles."""
    
    def __init__(self, interval_hours=24):
        self.interval_hours = interval_hours
        self.running = False
        self.thread = None
    
    def start(self):
        """Start the background scheduler."""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._scheduler_loop)
            self.thread.daemon = True
            self.thread.start()
    
    def stop(self):
        """Stop the background scheduler."""
        self.running = False
        if self.thread:
            self.thread.join()
    
    def _scheduler_loop(self):
        """Main scheduler loop."""
        while self.running:
            try:
                # Check if it's time to run
                if self._should_run_cycle():
                    print("Starting scheduled automation cycle...")
                    automation_service.run_automation_cycle()
                
                # Sleep for 1 hour before checking again
                time.sleep(3600)
                
            except Exception as e:
                print(f"Scheduler error: {e}")
                time.sleep(3600)  # Wait an hour before retrying
    
    def _should_run_cycle(self):
        """Check if automation cycle should run."""
        if automation_service.is_running:
            return False
        
        if not automation_service.last_run:
            return True  # Never run before
        
        # Check if enough time has passed
        time_since_last = datetime.now() - automation_service.last_run
        return time_since_last >= timedelta(hours=self.interval_hours)

# New sync and scheduler management routes
@automation_blueprint.route('/sync_modules', methods=['POST'])
def sync_modules_manually():
    """Manually trigger module sync."""
    try:
        sync_result = automation_service.sync_manager.sync_uploaded_modules()
        
        flash(f'Sync completed: {sync_result["processed_modules"]} modules processed, '
              f'{sync_result["deduplicated_modules"]} deduplicated', 'success')
        
        return jsonify({
            'status': 'success',
            'result': sync_result
        })
    except Exception as e:
        logger.error(f"Manual sync failed: {str(e)}")
        flash(f'Sync failed: {str(e)}', 'error')
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@automation_blueprint.route('/scheduler/status')
def scheduler_status():
    """Get scheduler status."""
    try:
        scheduler = get_scheduler()
        status = scheduler.get_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_blueprint.route('/scheduler/trigger', methods=['POST'])
def trigger_scheduler():
    """Trigger immediate scheduler run."""
    try:
        scheduler = get_scheduler()
        result = scheduler.trigger_immediate_run()
        
        flash('Hourly tasks triggered successfully', 'success')
        return jsonify(result)
    except Exception as e:
        logger.error(f"Scheduler trigger failed: {str(e)}")
        flash(f'Scheduler trigger failed: {str(e)}', 'error')
        return jsonify({'error': str(e)}), 500

@automation_blueprint.route('/sync_status')
def sync_status():
    """Get current sync status."""
    try:
        status = automation_service.sync_manager.get_sync_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Global scheduler instance
automation_scheduler = AutomationScheduler()

def init_automation_integration(app):
    """Initialize automation integration with Flask app."""
    app.register_blueprint(automation_blueprint)
    
    # Start background scheduler
    automation_scheduler.start()
    
    # Initialize automation service
    automation_service.initialize()
    
    print("Automation integration initialized")