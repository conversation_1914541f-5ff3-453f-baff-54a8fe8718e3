{% extends "base.html" %}
{% set active_page = "github-integration" %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-0">GitHub Integration</h1>
            <p class="text-muted mb-0">Pull fresh modules from GitHub and sync upgraded modules back</p>
        </div>
        <div>
            <button class="btn btn-outline-secondary" onclick="refreshRepositories()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Authentication Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" id="authCard">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div id="authIcon" class="me-3">
                            <i class="fab fa-github fa-3x text-secondary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="card-title mb-1" id="authTitle">GitHub Authentication</h5>
                            <p class="card-text text-muted mb-0" id="authStatus">Checking authentication status...</p>
                        </div>
                        <div id="authActions">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#authModal">
                                <i class="fas fa-key"></i> Configure
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Repository Management -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">Your Repositories</h5>
                </div>
                <div class="card-body">
                    <div id="repositoryList">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                            <p>Loading repositories...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">Custom Repository</h5>
                </div>
                <div class="card-body">
                    <form id="customRepoForm">
                        <div class="mb-3">
                            <label for="repoUrl" class="form-label">Repository URL</label>
                            <input type="url" class="form-control" id="repoUrl" 
                                   placeholder="https://github.com/username/repository">
                            <div class="form-text">Enter any public or accessible GitHub repository URL</div>
                        </div>
                        <div class="mb-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> This will pull all modules from the repository in their original versions.
                                Use the Migration Orchestrator to upgrade them to your desired target version.
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="scanRepository()">
                                <i class="fas fa-search"></i> Scan Repository
                            </button>
                            <button type="button" class="btn btn-success" onclick="pullModules()">
                                <i class="fas fa-download"></i> Pull All Modules
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scan Results -->
    <div class="row mb-4" id="scanResultsRow" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Detected Modules</h5>
                </div>
                <div class="card-body">
                    <div id="scanResults"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pull Results -->
    <div class="row mb-4" id="pullResultsRow" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Pull Results</h5>
                </div>
                <div class="card-body">
                    <div id="pullResults"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sync Upgraded Modules Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        Sync Upgraded Modules to GitHub
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Push completed migration results back to GitHub in organized folders.</p>

                    <div class="mb-3">
                        <label class="form-label">Target Branch</label>
                        <input type="text" class="form-control" id="targetBranch" value="upgraded-modules"
                               placeholder="Branch name for upgraded modules">
                        <div class="form-text">Upgraded modules will be organized as: upgraded_modules/{version}/{module_name}/</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Completed Migrations</label>
                        <div id="completedMigrations">
                            <div class="text-muted">Loading completed migrations...</div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-success" onclick="loadCompletedMigrations()">
                            <i class="fas fa-refresh"></i> Refresh Completed Migrations
                        </button>
                        <button type="button" class="btn btn-success" onclick="syncUpgradedModules()" disabled id="syncButton">
                            <i class="fas fa-cloud-upload-alt"></i> Sync Selected to GitHub
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Sync Information
                    </h5>
                </div>
                <div class="card-body">
                    <h6>How Sync Works:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Pulls fresh modules from GitHub</li>
                        <li><i class="fas fa-check text-success me-2"></i>Processes through TrueMigrationOrchestrator</li>
                        <li><i class="fas fa-check text-success me-2"></i>Syncs upgraded modules back to separate folder</li>
                    </ul>

                    <h6 class="mt-3">Folder Structure:</h6>
                    <div class="bg-light p-2 rounded">
                        <code>
                            upgraded_modules/<br>
                            ├── 18.0/<br>
                            │   ├── module_name_1/<br>
                            │   └── module_name_2/<br>
                            └── 17.0/<br>
                                └── module_name_3/
                        </code>
                    </div>

                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <small>Ensure your GitHub token has write permissions to the repository.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Authentication Modal -->
<div class="modal fade" id="authModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">GitHub Authentication</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>GitHub Token Required</strong><br>
                    To access private repositories and increase API limits, you need to provide a GitHub Personal Access Token.
                </div>
                
                <form id="authForm">
                    <div class="mb-3">
                        <label for="githubToken" class="form-label">GitHub Token</label>
                        <input type="password" class="form-control" id="githubToken" 
                               placeholder="ghp_xxxxxxxxxxxxxxxxxxxx">
                        <div class="form-text">
                            <a href="https://github.com/settings/tokens" target="_blank">
                                Create a new token
                            </a> with 'repo' scope for private repositories
                        </div>
                    </div>
                </form>
                
                <div class="mt-3">
                    <h6>Token Permissions Needed:</h6>
                    <ul class="small">
                        <li><code>repo</code> - Access to private repositories</li>
                        <li><code>public_repo</code> - Access to public repositories</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="testAuthentication()">Test Authentication</button>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let currentScanResults = [];
let authenticationStatus = false;

// Load data on page load
document.addEventListener('DOMContentLoaded', function() {
    checkAuthenticationStatus();
});

function checkAuthenticationStatus() {
    fetch('/api/github/auth-status')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.authenticated) {
                updateAuthenticationStatus(true);
                loadRepositories();
            } else {
                updateAuthenticationStatus(false);
                displayAuthRequired();
            }
        })
        .catch(error => {
            console.error('Error checking authentication:', error);
            updateAuthenticationStatus(false);
            showAlert('error', 'Failed to check GitHub authentication status');
        });
}

function updateAuthenticationStatus(isAuthenticated) {
    authenticationStatus = isAuthenticated;
    const authIcon = document.getElementById('authIcon');
    const authTitle = document.getElementById('authTitle');
    const authStatus = document.getElementById('authStatus');
    const authActions = document.getElementById('authActions');
    const authCard = document.getElementById('authCard');

    if (isAuthenticated) {
        authIcon.innerHTML = '<i class="fab fa-github fa-3x text-success"></i>';
        authTitle.textContent = 'GitHub Connected';
        authStatus.textContent = 'Successfully authenticated with GitHub';
        authActions.innerHTML = '<span class="badge bg-success">Connected</span>';
        authCard.className = 'card border-success';
    } else {
        authIcon.innerHTML = '<i class="fab fa-github fa-3x text-warning"></i>';
        authTitle.textContent = 'GitHub Authentication Required';
        authStatus.textContent = 'Configure GitHub token to access repositories';
        authActions.innerHTML = `
            <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#authModal">
                <i class="fas fa-key"></i> Configure
            </button>
        `;
        authCard.className = 'card border-warning';
    }
}

function loadRepositories() {
    fetch('/api/github/repositories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRepositories(data.repositories);
            } else {
                showAlert('error', 'Failed to load repositories');
                displayAuthRequired();
            }
        })
        .catch(error => {
            console.error('Error loading repositories:', error);
            showAlert('error', 'Failed to load repositories');
            displayAuthRequired();
        });
}

function displayRepositories(repositories) {
    const container = document.getElementById('repositoryList');
    
    if (repositories.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-folder-open fa-2x mb-2"></i>
                <p>No repositories found</p>
            </div>
        `;
        return;
    }

    let html = '<div class="list-group">';
    repositories.forEach(repo => {
        const languageBadge = repo.language ? 
            `<span class="badge bg-secondary ms-2">${repo.language}</span>` : '';
        const privateBadge = repo.private ? 
            '<span class="badge bg-warning ms-1">Private</span>' : '';
        
        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${repo.name}${privateBadge}${languageBadge}</h6>
                        <p class="mb-1 small text-muted">${repo.description || 'No description'}</p>
                        <small class="text-muted">${repo.full_name}</small>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="scanRepositoryByUrl('${repo.clone_url}')">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="pullRepositoryModules('${repo.clone_url}')">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function displayAuthRequired() {
    const container = document.getElementById('repositoryList');
    container.innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="fas fa-lock fa-2x mb-2"></i>
            <p>GitHub authentication required</p>
            <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#authModal">
                Configure Token
            </button>
        </div>
    `;
}

function testAuthentication() {
    const token = document.getElementById('githubToken').value;
    
    if (!token) {
        showAlert('error', 'Please enter a GitHub token');
        return;
    }

    fetch('/api/github-authenticate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token: token })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'GitHub authentication successful!');
            bootstrap.Modal.getInstance(document.getElementById('authModal')).hide();
            checkAuthenticationStatus();
        } else {
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        console.error('Error testing authentication:', error);
        showAlert('error', 'Failed to test authentication');
    });
}

function scanRepository() {
    const repoUrl = document.getElementById('repoUrl').value;
    if (!repoUrl) {
        showAlert('error', 'Please enter a repository URL');
        return;
    }
    
    scanRepositoryByUrl(repoUrl);
}

function scanRepositoryByUrl(repoUrl) {
    showAlert('info', 'Scanning repository for Odoo modules...');
    
    fetch('/api/github-scan-repository', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ repository_url: repoUrl })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentScanResults = data.modules;
            displayScanResults(data);
            showAlert('success', `Found ${data.count} modules in repository`);
        } else {
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        console.error('Error scanning repository:', error);
        showAlert('error', 'Failed to scan repository');
    });
}

function displayScanResults(data) {
    const resultsRow = document.getElementById('scanResultsRow');
    const resultsContainer = document.getElementById('scanResults');
    
    if (data.modules.length === 0) {
        resultsContainer.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-2x mb-2"></i>
                <p>No Odoo modules detected in this repository</p>
            </div>
        `;
    } else {
        let html = `
            <div class="alert alert-info mb-3">
                <i class="fas fa-info-circle"></i>
                Found <strong>${data.count}</strong> Odoo modules in <code>${data.repository_url}</code>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Module Name</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Path</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        data.modules.forEach(module => {
            const size = module.size ? formatFileSize(module.size) : '-';
            const typeIcon = module.type === 'directory' ? 
                '<i class="fas fa-folder text-primary"></i>' : 
                '<i class="fas fa-file-archive text-info"></i>';
            
            html += `
                <tr>
                    <td><strong>${module.name}</strong></td>
                    <td>${typeIcon} ${module.type}</td>
                    <td>${size}</td>
                    <td><code>${module.path}</code></td>
                </tr>
            `;
        });
        
        html += '</tbody></table></div>';
        resultsContainer.innerHTML = html;
    }
    
    resultsRow.style.display = 'block';
}

function pullModules() {
    const repoUrl = document.getElementById('repoUrl').value;
    if (!repoUrl) {
        showAlert('error', 'Please enter a repository URL');
        return;
    }
    
    pullRepositoryModules(repoUrl);
}

function pullRepositoryModules(repoUrl) {
    // No target versions needed - just pull fresh modules
    
    showAlert('info', 'Pulling modules from repository... This may take a few minutes.');
    
    fetch('/api/github/pull-modules', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            repository_url: repoUrl
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPullResults(data.results);
            showAlert('success', `Successfully processed ${data.results.modules_processed} modules`);
        } else {
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        console.error('Error pulling modules:', error);
        showAlert('error', 'Failed to pull modules from repository');
    });
}

function displayPullResults(results) {
    const resultsRow = document.getElementById('pullResultsRow');
    const resultsContainer = document.getElementById('pullResults');
    
    let html = `
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-primary">${results.modules_detected}</h4>
                        <small>Detected</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-info">${results.modules_downloaded}</h4>
                        <small>Downloaded</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-success">${results.modules_processed}</h4>
                        <small>Processed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-danger">${results.modules_failed}</h4>
                        <small>Failed</small>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    if (results.processed_modules.length > 0) {
        html += `
            <h6>Successfully Processed Modules:</h6>
            <div class="table-responsive mb-3">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Module</th>
                            <th>Size</th>
                            <th>Analysis Results</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        results.processed_modules.forEach(module => {
            html += `
                <tr>
                    <td><strong>${module.original_name}</strong></td>
                    <td>${formatFileSize(module.file_size)}</td>
                    <td>
                        ${module.analysis_results.map(result => 
                            `<span class="badge bg-secondary">${result.target_version}: ${result.compatibility_score}%</span>`
                        ).join(' ')}
                    </td>
                </tr>
            `;
        });
        
        html += '</tbody></table></div>';
    }
    
    if (results.failed_modules.length > 0) {
        html += `
            <h6>Failed Modules:</h6>
            <div class="alert alert-warning">
                <ul class="mb-0">
                    ${results.failed_modules.map(module => 
                        `<li><strong>${module.name}</strong>: ${module.error}</li>`
                    ).join('')}
                </ul>
            </div>
        `;
    }
    
    resultsContainer.innerHTML = html;
    resultsRow.style.display = 'block';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function refreshRepositories() {
    showAlert('info', 'Refreshing repositories...');
    checkAuthenticationStatus();
}

function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
    const alertHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show alert-auto-dismiss" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHTML);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert-auto-dismiss');
        if (alert) {
            bootstrap.Alert.getInstance(alert)?.close();
        }
    }, 5000);
}

// Sync Upgraded Modules Functions
function loadCompletedMigrations() {
    fetch('/api/migration-jobs')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCompletedMigrations(data.jobs.filter(job => job.status === 'COMPLETED'));
        } else {
            showAlert('error', 'Failed to load completed migrations');
        }
    })
    .catch(error => {
        console.error('Error loading migrations:', error);
        showAlert('error', 'Failed to load completed migrations');
    });
}

function displayCompletedMigrations(completedJobs) {
    const container = document.getElementById('completedMigrations');
    const syncButton = document.getElementById('syncButton');

    if (completedJobs.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-info-circle fa-2x mb-2"></i>
                <p>No completed migrations found</p>
                <small>Complete some migrations first to sync them back to GitHub</small>
            </div>
        `;
        syncButton.disabled = true;
        return;
    }

    let html = '<div class="list-group">';
    completedJobs.forEach(job => {
        html += `
            <div class="list-group-item">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${job.id}" id="migration_${job.id}">
                    <label class="form-check-label" for="migration_${job.id}">
                        <strong>${job.module_name}</strong>
                        <br>
                        <small class="text-muted">
                            ${job.original_version} → ${job.target_version}
                            <span class="badge bg-success ms-2">Completed</span>
                        </small>
                    </label>
                </div>
            </div>
        `;
    });
    html += '</div>';

    container.innerHTML = html;
    syncButton.disabled = false;
}

function syncUpgradedModules() {
    const selectedMigrations = [];
    document.querySelectorAll('#completedMigrations input[type="checkbox"]:checked').forEach(checkbox => {
        selectedMigrations.push(parseInt(checkbox.value));
    });

    if (selectedMigrations.length === 0) {
        showAlert('warning', 'Please select at least one completed migration to sync');
        return;
    }

    const targetBranch = document.getElementById('targetBranch').value || 'upgraded-modules';

    showAlert('info', 'Syncing upgraded modules to GitHub... This may take a few minutes.');

    fetch('/api/github/sync-upgraded', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            module_ids: selectedMigrations,
            target_branch: targetBranch
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // Refresh the completed migrations list
            loadCompletedMigrations();
        } else {
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        console.error('Error syncing modules:', error);
        showAlert('error', 'Failed to sync modules to GitHub');
    });
}

// Load completed migrations on page load
document.addEventListener('DOMContentLoaded', function() {
    loadCompletedMigrations();
});
</script>
{% endblock %}