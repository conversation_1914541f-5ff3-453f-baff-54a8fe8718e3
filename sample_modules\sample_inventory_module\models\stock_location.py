from odoo import models, fields, api
from odoo.exceptions import ValidationError


class StockLocation(models.Model):
    _inherit = 'stock.location'
    
    # New fields for enhanced location management
    location_type = fields.Selection([
        ('warehouse', 'Warehouse'),
        ('storage', 'Storage Area'),
        ('picking', 'Picking Zone'),
        ('shipping', 'Shipping Dock'),
    ], string='Location Type', default='storage')
    
    capacity = fields.Float(string='Maximum Capacity (m³)', default=0.0)
    current_usage = fields.Float(string='Current Usage (%)', compute='_compute_current_usage')
    temperature_controlled = fields.Boolean(string='Temperature Controlled', default=False)
    temperature_min = fields.Float(string='Min Temperature (°C)')
    temperature_max = fields.Float(string='Max Temperature (°C)')
    
    # Enhanced location tracking
    last_inventory_date = fields.Datetime(string='Last Inventory Count')
    inventory_frequency = fields.Integer(string='Inventory Frequency (days)', default=30)
    next_inventory_date = fields.Datetime(string='Next Inventory Date', compute='_compute_next_inventory')
    
    @api.depends('quant_ids', 'capacity')
    def _compute_current_usage(self):
        for location in self:
            if location.capacity > 0:
                total_volume = sum(location.quant_ids.mapped('volume')) or 0.0
                location.current_usage = (total_volume / location.capacity) * 100
            else:
                location.current_usage = 0.0
    
    @api.depends('last_inventory_date', 'inventory_frequency')
    def _compute_next_inventory(self):
        for location in self:
            if location.last_inventory_date and location.inventory_frequency:
                from datetime import timedelta
                location.next_inventory_date = location.last_inventory_date + timedelta(days=location.inventory_frequency)
            else:
                location.next_inventory_date = False
    
    @api.constrains('temperature_min', 'temperature_max')
    def _check_temperature_range(self):
        for location in self:
            if location.temperature_controlled and location.temperature_min >= location.temperature_max:
                raise ValidationError("Minimum temperature must be lower than maximum temperature.")
    
    def action_schedule_inventory(self):
        """Schedule inventory count for this location"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Schedule Inventory',
            'res_model': 'stock.inventory',
            'view_mode': 'form',
            'context': {
                'default_location_ids': [(6, 0, [self.id])],
            },
            'target': 'new',
        }