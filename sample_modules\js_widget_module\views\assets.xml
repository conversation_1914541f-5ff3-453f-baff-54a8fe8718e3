<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Template for the demo widget -->
        <template id="DemoWidget" name="Demo Widget Template">
            <div class="demo-widget">
                <div class="demo-widget__header">
                    <h3>JavaScript Demo Widget</h3>
                    <span class="badge badge-info">Odoo 18 Compatible</span>
                </div>
                
                <div class="demo-widget__counter" t-att-class="state.updated ? 'updated' : ''">
                    <div class="count-display" t-esc="state.counter"/>
                    <div class="message" t-esc="displayMessage"/>
                </div>
                
                <div class="demo-widget__controls">
                    <button class="btn-increment" t-on-click="increment">
                        <i class="fa fa-plus"/> Increment
                    </button>
                    <button class="btn-reset" t-on-click="reset">
                        <i class="fa fa-refresh"/> Reset
                    </button>
                </div>
            </div>
        </template>

        <!-- Action to display the widget -->
        <record id="action_demo_widget" model="ir.actions.client">
            <field name="name">JavaScript Demo Widget</field>
            <field name="tag">demo_widget</field>
            <field name="target">current</field>
        </record>

        <!-- Menu item -->
        <menuitem id="menu_demo_widget" 
                  name="JS Demo" 
                  action="action_demo_widget"
                  parent="base.menu_administration"
                  sequence="100"/>

    </data>
</odoo>