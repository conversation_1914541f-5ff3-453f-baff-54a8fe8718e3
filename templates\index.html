{% extends "base.html" %}
{% set title = "Dashboard" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-4">
            <i class="fas fa-tachometer-alt me-3"></i>
            Dashboard
        </h1>
        <p class="lead">Odoo Module Analysis & Version Migration Platform</p>
    </div>
</div>

<!-- Status Cards -->
<div class="row mb-4">
    <!-- Docker Environments Status -->
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fab fa-docker fa-3x text-info"></i>
                </div>
                <h5 class="card-title">Docker Environments</h5>
                <p class="card-text">
                    <span class="badge bg-info">Multi-Version Support</span>
                </p>
                <small class="text-muted">Odoo v13 - v18 Testing</small>
                <div class="mt-3">
                    <a href="{{ url_for('main.docker_environments') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fab fa-docker me-1"></i>Manage
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Statistics -->
    <div class="col-md-8 mb-3">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-cubes me-2"></i>
                    Module Statistics
                </h5>
                <div class="row text-center">
                    <div class="col-3">
                        <div class="mb-2">
                            <i class="fas fa-archive fa-2x text-primary"></i>
                        </div>
                        <h4 class="mb-1">{{ total_modules }}</h4>
                        <small class="text-muted">Total Modules</small>
                    </div>
                    <div class="col-3">
                        <div class="mb-2">
                            <i class="fas fa-check-circle fa-2x text-success"></i>
                        </div>
                        <h4 class="mb-1">{{ analyzed_modules }}</h4>
                        <small class="text-muted">Analyzed</small>
                    </div>
                    <div class="col-3">
                        <div class="mb-2">
                            <i class="fas fa-clock fa-2x text-warning"></i>
                        </div>
                        <h4 class="mb-1">{{ pending_modules }}</h4>
                        <small class="text-muted">Pending</small>
                    </div>
                    <div class="col-3">
                        <div class="mb-2">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                        </div>
                        <h4 class="mb-1">{{ error_modules }}</h4>
                        <small class="text-muted">Errors</small>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('main.analyze_modules') }}" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>View Analysis
                    </a>
                    <a href="{{ url_for('main.upload_modules') }}" class="btn btn-outline-primary">
                        <i class="fas fa-upload me-1"></i>Upload Modules
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
                <div class="d-flex flex-wrap gap-2">
                    {% if not odoo_installation or odoo_installation.status != 'active' %}
                        <a href="{{ url_for('main.install_odoo') }}" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>Install Odoo
                        </a>
                    {% endif %}
                    
                    <a href="{{ url_for('main.upload_modules') }}" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>Upload Modules
                    </a>
                    
                    {% if pending_modules > 0 %}
                        <a href="{{ url_for('main.analyze_all') }}" class="btn btn-warning">
                            <i class="fas fa-cogs me-1"></i>Analyze All Pending
                        </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-secondary" onclick="refreshStatus()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh Status
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Modules -->
{% if recent_modules %}
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Recent Uploads
                </h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Module Name</th>
                                <th>Upload Date</th>
                                <th>File Size</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for module in recent_modules %}
                            <tr>
                                <td>
                                    <i class="fas fa-cube me-2 text-primary"></i>
                                    {{ module.name }}
                                </td>
                                <td>{{ module.upload_date.strftime('%Y-%m-%d %H:%M') if module.upload_date }}</td>
                                <td>{{ (module.file_size / 1024 / 1024) | round(2) }} MB</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if module.analysis_status == 'completed' else 'warning' if module.analysis_status == 'pending' else 'info' if module.analysis_status == 'analyzing' else 'danger' }}">
                                        {{ module.analysis_status.title() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('main.module_details', module_id=module.id) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if module.analysis_status == 'pending' %}
                                            <a href="{{ url_for('main.orchestrate_migration_form', module_id=module.id) }}" class="btn btn-outline-success" title="True Migration System">
                                                <i class="fas fa-robot"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
function refreshStatus() {
    location.reload();
}
</script>
{% endblock %}
