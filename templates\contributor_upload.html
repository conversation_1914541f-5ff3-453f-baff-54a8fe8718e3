{% extends "base.html" %}

{% block title %}Contribute Modules - Odoo Module Analyzer{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users me-2"></i>Contribute Odoo Modules</h2>
                <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
    
    <!-- Information Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>How to Contribute</h5>
                <p class="mb-3">Help us build a comprehensive collection of upgraded Odoo modules! Upload your modules and our system will:</p>
                <ul class="mb-0">
                    <li><strong>Automatically detect the source version</strong> (v13, v14, v15, v16, v17, v18)</li>
                    <li><strong>Place modules in the correct folders</strong> to prevent misorganization</li>
                    <li><strong>Progressively upgrade</strong> through the version chain: v13→v14→v15→v16→v17→v18</li>
                    <li><strong>Apply quality assurance</strong> and compatibility fixes</li>
                    <li><strong>Commit to GitHub repository</strong> for community access</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Version Guide -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-code-branch me-2"></i>Version Detection Guide</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Our system automatically detects versions by analyzing:</h6>
                            <ul>
                                <li><strong>API patterns:</strong> @api.one, @api.multi (v13-v14)</li>
                                <li><strong>Import statements:</strong> from openerp vs from odoo</li>
                                <li><strong>Asset structure:</strong> manifest.py assets (v15+)</li>
                                <li><strong>JavaScript frameworks:</strong> OWL components (v16+)</li>
                                <li><strong>File organization:</strong> __openerp__.py vs __manifest__.py</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Don't worry about version uncertainty!</h6>
                            <div class="alert alert-success">
                                <i class="fas fa-robot me-2"></i>
                                <strong>Smart Detection:</strong> Even if your module doesn't specify a version or uses custom versioning, our AI-powered analyzer will determine the correct source version and place it in the appropriate folder.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Upload Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cloud-upload-alt me-2"></i>Upload Modules</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="contributorUploadForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="modules" class="form-label">Select Module Files</label>
                                    <input type="file" class="form-control" id="modules" name="modules" 
                                           multiple accept=".zip,.tar,.tar.gz,.tar.bz2" required>
                                    <div class="form-text">
                                        Supported formats: ZIP, TAR, TAR.GZ, TAR.BZ2 | Max size per file: 100MB
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="contributor_name" class="form-label">Your Name/Organization</label>
                                    <input type="text" class="form-control" id="contributor_name" name="contributor_name" 
                                           placeholder="e.g., John Doe or Acme Corp" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="contributor_email" class="form-label">Contact Email</label>
                                    <input type="email" class="form-control" id="contributor_email" name="contributor_email" 
                                           placeholder="<EMAIL>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Module Description (Optional)</label>
                                    <textarea class="form-control" id="description" name="description" rows="3" 
                                              placeholder="Brief description of the modules you're contributing..."></textarea>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="auto_detect" name="auto_detect" checked>
                                    <label class="form-check-label" for="auto_detect">
                                        <strong>Enable smart version detection</strong> (Recommended)
                                    </label>
                                    <div class="form-text">
                                        Let our system automatically detect and place modules in correct version folders
                                    </div>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="auto_upgrade" name="auto_upgrade" checked>
                                    <label class="form-check-label" for="auto_upgrade">
                                        <strong>Enable automatic upgrades</strong> (Recommended)
                                    </label>
                                    <div class="form-text">
                                        Automatically upgrade modules through the version pipeline (v13→v14→v15→v16→v17→v18)
                                    </div>
                                </div>
                                
                                <div class="form-check mb-4">
                                    <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                    <label class="form-check-label" for="agree_terms">
                                        I agree to contribute these modules under an open-source license and confirm I have the rights to do so
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6><i class="fas fa-info-circle me-2"></i>What Happens Next?</h6>
                                        <ol class="small">
                                            <li><strong>Upload & Analysis</strong><br>
                                                Your modules are uploaded and analyzed
                                            </li>
                                            <li><strong>Version Detection</strong><br>
                                                System determines source Odoo version
                                            </li>
                                            <li><strong>Folder Placement</strong><br>
                                                Modules placed in correct version folder
                                            </li>
                                            <li><strong>Automatic Upgrade</strong><br>
                                                Progressive upgrading through versions
                                            </li>
                                            <li><strong>Quality Check</strong><br>
                                                Compatibility validation and fixes
                                            </li>
                                            <li><strong>GitHub Commit</strong><br>
                                                Upgraded modules committed to repository
                                            </li>
                                            <li><strong>Notification</strong><br>
                                                You receive confirmation email
                                            </li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="fas fa-upload me-1"></i>Contribute Modules
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Progress Section (Hidden by default) -->
    <div class="row mt-4" id="progressSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Processing Your Contribution</h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" 
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%" id="uploadProgress">
                        </div>
                    </div>
                    <div id="progressText">Initializing upload...</div>
                    <div id="versionDetectionResults" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('contributorUploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('submitBtn');
    const progressSection = document.getElementById('progressSection');
    const progressBar = document.getElementById('uploadProgress');
    const progressText = document.getElementById('progressText');
    
    // Show progress section
    progressSection.style.display = 'block';
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
    
    // Create FormData
    const formData = new FormData(this);
    
    // Upload with progress tracking
    const xhr = new XMLHttpRequest();
    
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            progressBar.style.width = percentComplete + '%';
            progressBar.setAttribute('aria-valuenow', percentComplete);
            progressText.textContent = `Uploading... ${Math.round(percentComplete)}%`;
        }
    });
    
    xhr.addEventListener('load', function() {
        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-success');
                progressText.innerHTML = '<i class="fas fa-check-circle text-success me-2"></i>Upload successful! Processing modules...';
                
                // Show version detection results if available
                if (response.detected_versions) {
                    const resultsDiv = document.getElementById('versionDetectionResults');
                    let html = '<h6>Version Detection Results:</h6><ul>';
                    for (const [module, version] of Object.entries(response.detected_versions)) {
                        html += `<li><strong>${module}:</strong> Detected as Odoo ${version}</li>`;
                    }
                    html += '</ul>';
                    resultsDiv.innerHTML = html;
                }
                
                setTimeout(() => {
                    window.location.href = '{{ url_for("main.analyze_modules") }}';
                }, 3000);
            } else {
                progressBar.classList.add('bg-danger');
                progressText.innerHTML = '<i class="fas fa-exclamation-triangle text-danger me-2"></i>Upload failed: ' + response.error;
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-upload me-1"></i>Try Again';
            }
        } else {
            progressBar.classList.add('bg-danger');
            progressText.innerHTML = '<i class="fas fa-exclamation-triangle text-danger me-2"></i>Upload failed. Please try again.';
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-upload me-1"></i>Try Again';
        }
    });
    
    xhr.addEventListener('error', function() {
        progressBar.classList.add('bg-danger');
        progressText.innerHTML = '<i class="fas fa-exclamation-triangle text-danger me-2"></i>Network error. Please check your connection and try again.';
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-upload me-1"></i>Try Again';
    });
    
    xhr.open('POST', '{{ url_for("main.contributor_upload") }}');
    xhr.send(formData);
});
</script>
{% endblock %}