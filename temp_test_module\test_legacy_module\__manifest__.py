# -*- coding: utf-8 -*-
{
    'name': 'Legacy Test Module',
    'version': '********.0',
    'category': 'Tools',
    'summary': 'Test module with legacy patterns for upgrade testing',
    'description': """
This module contains legacy patterns that need upgrading:
- Old API decorators (@api.one, @api.multi)
- Legacy field definitions
- Deprecated XML attributes
- Old JavaScript patterns
    """,
    'author': 'Test Author',
    'depends': ['base', 'web'],
    'data': [
        'security/ir.model.access.csv',
        'views/test_views.xml',
        'data/test_data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'test_legacy_module/static/src/js/test_widget.js',
            'test_legacy_module/static/src/css/test_style.css',
        ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
}