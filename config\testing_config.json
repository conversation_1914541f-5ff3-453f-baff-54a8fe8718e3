{"docker": {"enabled": true, "base_images": {"13.0": "odoo:13.0", "14.0": "odoo:14.0", "15.0": "odoo:15.0", "16.0": "odoo:16.0", "17.0": "odoo:17.0", "18.0": "odoo:18.0"}, "timeout": 300, "memory_limit": "2g", "cpu_limit": "2"}, "runbot": {"enabled": true, "base_url": "https://runbot.odoo.com", "api_key": null, "timeout": 600, "versions": ["13.0", "14.0", "15.0", "16.0", "17.0", "18.0"]}, "ai_analysis": {"enabled": true, "model": "gpt-4", "max_tokens": 2000, "temperature": 0.1}, "testing": {"parallel_tests": 3, "retry_count": 2, "log_level": "INFO", "auto_fix_enabled": true, "quality_threshold": 85.0}, "validation": {"manifest_checks": true, "syntax_validation": true, "dependency_analysis": true, "security_scan": true, "performance_test": true}, "reporting": {"detailed_logs": true, "ai_insights": true, "fix_suggestions": true, "export_formats": ["json", "html", "pdf"]}}