/** @odoo-module **/

import { Component, useState } from "@odoo/owl";
import { registry } from "@web/core/registry";

// Modern Odoo 18 widget using OWL framework
export class DemoWidget extends Component {
    setup() {
        this.state = useState({
            counter: 0,
            message: "Hello Odoo 18!"
        });
    }

    increment() {
        this.state.counter++;
    }

    // Arrow function - ES6+ feature
    reset = () => {
        this.state.counter = 0;
    }

    get displayMessage() {
        return `${this.state.message} Count: ${this.state.counter}`;
    }
}

DemoWidget.template = "js_widget_module.DemoWidget";

// Legacy patterns that would trigger warnings/issues
class LegacyWidget {
    constructor() {
        // Old jQuery patterns
        $(document).ready(function() {
            console.log("Legacy jQuery ready");
        });
        
        // Deprecated OpenERP patterns
        if (typeof openerp !== 'undefined') {
            var instance = openerp.web;
            instance.Widget.extend({
                start: function() {
                    return this._super();
                }
            });
        }
        
        // Old web.Widget usage
        if (typeof web !== 'undefined') {
            web.Widget.extend({
                init: function() {
                    this._super.apply(this, arguments);
                }
            });
        }
        
        // Old RPC patterns
        this.rpc.query({
            model: 'res.partner',
            method: 'search_read'
        });
    }
}

// Modern ES6+ features
const modernFeatures = {
    // Template literals
    message: `Welcome to Odoo ${new Date().getFullYear()}`,
    
    // Destructuring
    getConfig: ({ host, port = 8069 } = {}) => {
        return { host, port };
    },
    
    // Async/await
    async fetchData() {
        try {
            const response = await fetch('/web/dataset/call_kw');
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Fetch error:', error);
        }
    },
    
    // Map and Set
    cache: new Map(),
    uniqueIds: new Set()
};

// AJAX patterns for compatibility testing
const ajaxPatterns = {
    // Modern approach
    modernRpc() {
        return this.env.services.rpc({
            model: 'res.partner',
            method: 'search_read',
            args: []
        });
    },
    
    // Legacy jsonRpc
    legacyRpc() {
        return ajax.jsonRpc('/web/dataset/call_kw', 'call', {
            model: 'res.partner',
            method: 'search_read'
        });
    }
};

registry.category("actions").add("demo_widget", DemoWidget);