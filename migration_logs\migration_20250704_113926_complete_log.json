{"migration_id": "migration_20250704_113926", "success": false, "source_version": "15.0", "target_version": "17.0", "include_database": false, "dry_run": false, "phases": {"analysis": {"completed": true, "basic_analysis": {"name": "Universal Appointments: HR Bridge", "version": "********.5", "author": "faOtools", "website": "https://faotools.com/apps/15.0/universal-appointments-hr-bridge-15-0-business-appointment-hr-610", "summary": "The extension to the Universal Appointments app to apply employees as appointment resources", "description": "\nFor the full details look at static/description/index.html\n* Features *\n#odootools_proprietary", "category": "Extra Tools", "odoo_version": "15.0", "depends": ["hr", "business_appointment"], "external_dependencies": {}, "has_manifest": true, "has_models": true, "has_views": true, "has_controllers": true, "has_static": true, "has_security": true, "has_data": true, "compatibility_score": 74, "compatibility_issues": [], "compatibility_warnings": ["Potential deprecated attrs usage in business_resource.xml", "Older Odoo version (15.0) - minor migration may be required"], "file_structure": {"root": {"directories": ["controllers", "data", "models", "reports", "security", "static", "views", "wizard"], "files": ["LICENSE", "__init__.py", "__manifest__.py"], "python_files": ["__init__.py", "__manifest__.py"], "xml_files": [], "js_files": [], "css_files": [], "template_files": [], "config_files": [], "image_files": [], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}, "controllers": {"directories": [], "files": ["__init__.py"], "python_files": ["__init__.py"], "xml_files": [], "js_files": [], "css_files": [], "template_files": [], "config_files": [], "image_files": [], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}, "data": {"directories": [], "files": ["data.xml"], "python_files": [], "xml_files": ["data.xml"], "js_files": [], "css_files": [], "template_files": [], "config_files": [], "image_files": [], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}, "models": {"directories": [], "files": ["business_resource.py", "__init__.py"], "python_files": ["business_resource.py", "__init__.py"], "xml_files": [], "js_files": [], "css_files": [], "template_files": [], "config_files": [], "image_files": [], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}, "reports": {"directories": [], "files": ["__init__.py"], "python_files": ["__init__.py"], "xml_files": [], "js_files": [], "css_files": [], "template_files": [], "config_files": [], "image_files": [], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}, "security": {"directories": [], "files": ["ir.model.access.csv"], "python_files": [], "xml_files": [], "js_files": [], "css_files": [], "template_files": [], "config_files": [], "image_files": [], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}, "static": {"directories": ["description"], "files": [], "python_files": [], "xml_files": [], "js_files": [], "css_files": [], "template_files": [], "config_files": [], "image_files": [], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}, "static/description": {"directories": [], "files": ["app_icon_1.png", "app_icon_2.png", "app_icon_3.png", "app_icon_4.png", "app_icon_5.png", "app_icon_6.png", "app_icon_7.png", "app_icon_8.png", "app_icon_9.png", "icon.png", "index.html", "main.png", "main_nopromo.png", "resource_employee_dentist.png"], "python_files": [], "xml_files": [], "js_files": [], "css_files": [], "template_files": ["index.html"], "config_files": [], "image_files": ["app_icon_1.png", "app_icon_2.png", "app_icon_3.png", "app_icon_4.png", "app_icon_5.png", "app_icon_6.png", "app_icon_7.png", "app_icon_8.png", "app_icon_9.png", "icon.png", "main.png", "main_nopromo.png", "resource_employee_dentist.png"], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}, "views": {"directories": [], "files": ["business_resource.xml"], "python_files": [], "xml_files": ["business_resource.xml"], "js_files": [], "css_files": [], "template_files": [], "config_files": [], "image_files": [], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}, "wizard": {"directories": [], "files": ["__init__.py"], "python_files": ["__init__.py"], "xml_files": [], "js_files": [], "css_files": [], "template_files": [], "config_files": [], "image_files": [], "doc_files": [], "test_files": [], "migration_files": [], "wizard_files": [], "report_files": []}}, "target_version": "17.0", "installable": true, "auto_install": false, "application": true}, "openupgrade_analysis": {"source_version": "15.0", "target_version": "17.0", "manifest_data": {"name": "Universal Appointments: HR Bridge", "version": "********.5", "category": "Extra Tools", "author": "faOtools", "website": "https://faotools.com/apps/15.0/universal-appointments-hr-bridge-15-0-business-appointment-hr-610", "license": "Other proprietary", "application": true, "installable": true, "auto_install": false, "depends": ["hr", "business_appointment"], "data": ["views/business_resource.xml", "security/ir.model.access.csv", "data/data.xml"], "js": [], "assets": {}, "demo": [], "external_dependencies": {}, "summary": "The extension to the Universal Appointments app to apply employees as appointment resources", "description": "\nFor the full details look at static/description/index.html\n* Features *\n#odootools_proprietary", "images": ["static/description/main.png"], "price": "0.0", "currency": "EUR", "live_test_url": "https://faotools.com/my/tickets/newticket?&url_app_id=136&ticket_version=15.0&url_type_id=3"}, "analysis_report": {"models_new": [], "models_removed": [], "fields_added": [], "fields_removed": [], "fields_changed": [], "xmlids_new": [], "xmlids_removed": [], "xmlids_changed": [], "dependencies_added": [], "dependencies_removed": [], "data_records_new": [], "data_records_removed": [], "workflow_changes": [], "migration_required": [{"type": "owl_migration", "action": "Convert legacy JavaScript to OWL components", "description": "Modernize frontend framework"}, {"type": "es6_modules", "action": "Convert to ES6 module syntax", "description": "Update JavaScript import/export patterns"}, {"type": "advanced_owl", "action": "Update to advanced OWL patterns", "description": "Optimize component architecture"}], "warnings": [], "recommendations": [], "models_found": [], "fields_found": {}, "xmlids_found": [{"id": "business_resource_view_search", "model": "ir.ui.view", "file": "views/business_resource.xml"}, {"id": "business_resource_view_form", "model": "ir.ui.view", "file": "views/business_resource.xml"}]}, "migration_complexity": "Medium", "estimated_effort": "3-5 days", "critical_issues": [], "analysis_files": {"openupgrade_analysis.txt": "# OpenUpgrade Analysis Report\n# Generated by Odoo Module Automation System\n\n## Models\n\n## Fields\n\n## XML IDs\n+ business_resource_view_search (ir.ui.view)\n+ business_resource_view_form (ir.ui.view)\n\n## Migration Requirements\n- owl_migration: Convert legacy JavaScript to OWL components\n- es6_modules: Convert to ES6 module syntax\n- advanced_owl: Update to advanced OWL patterns\n\n## Warnings", "openupgrade_analysis_work.txt": "# OpenUpgrade Analysis Report\n# Generated by Odoo Module Automation System\n\n## Models\n\n## Fields\n\n## XML IDs\n+ business_resource_view_search (ir.ui.view)\n+ business_resource_view_form (ir.ui.view)\n\n## Migration Requirements\n- owl_migration: Convert legacy JavaScript to OWL components\n- es6_modules: Convert to ES6 module syntax\n- advanced_owl: Update to advanced OWL patterns\n\n## Warnings\n\n# Work annotations:\n# TODO: Review and implement required changes\n", "pre-migration.py": "from openupgradelib import openupgrade\n\*********************()\ndef migrate(env, version):\n    \"\"\"Pre-migration script\"\"\"\n    # Add column renames, table renames, etc.\n    \n    # Example: Rename columns\n    # openupgrade.rename_columns(env.cr, {\n    #     'table_name': [\n    #         ('old_column', 'new_column', None),\n    #     ],\n    # })\n    \n    pass\n", "post-migration.py": "from openupgradelib import openupgrade\n\*********************()\ndef migrate(env, version):\n    \"\"\"Post-migration script\"\"\"\n    # Add data migrations, field migrations, etc.\n    \n    # Example: Load new data\n    # openupgrade.load_data(env.cr, 'module_name', 'data/data_file.xml')\n    \n    pass\n", "end-migration.py": "from openupgradelib import openupgrade\n\*********************()\ndef migrate(env, version):\n    \"\"\"End-migration script\"\"\"\n    # Final cleanup operations\n    \n    # Example: Clean up obsolete data\n    # openupgrade.clean_transient_models(env.cr)\n    \n    pass\n"}, "openupgrade_recommendations": ["1. Create backup of database before migration", "2. Test migration on copy of production database", "3. Review all identified warnings and migration requirements", "8. Test all JavaScript functionality after OWL migration", "9. Verify component interactions work correctly"]}, "migration_plan": {"version_progression": [["15.0", "16.0"], ["16.0", "17.0"]], "module_changes_required": [{"type": "owl_component_migration", "automated": false}, {"type": "es6_module_migration", "automated": true}], "database_changes_required": [{"type": "javascript_view_migration", "automated": true}], "manual_interventions": [], "testing_requirements": ["Functional testing of all module features", "Data integrity validation", "Performance regression testing", "User acceptance testing"], "rollback_strategy": {"database_backup_required": true, "module_backup_required": true, "rollback_time_estimate": "15-30 minutes", "rollback_complexity": "Medium"}}, "complexity_assessment": {"level": "Low", "score": 5, "factors": {"critical_issues": 0, "warnings": 0, "manual_interventions": 0, "database_changes": 1}, "estimated_time": "1-2 hours", "recommendation": "Migration can proceed with standard testing procedures"}, "warnings": [], "critical_issues": []}, "module_upgrade": {"completed": true, "upgraded_module_path": "/tmp/tmpg8d6dgsr/module_upgrade_upgraded_v17.0_20250704_113926.zip", "fixes_applied": [], "warnings": [], "backup_created": {"success": true, "backup_path": "/home/<USER>/workspace/uploads/backups/business_appointment_hr.zip_backup_20250704_113926", "timestamp": "2025-07-04T11:39:26.475782"}}, "database_migration": {"completed": true, "skipped": true, "message": "Database migration skipped per request"}, "validation": {"completed": true, "validations_passed": ["Module upgrade completed successfully", "Database migration completed successfully", "Target version compatibility verified"], "validations_failed": [], "overall_score": 100.0}, "cleanup": {"completed": false}}, "artifacts": {}, "warnings": [], "errors": [], "execution_time": 0}