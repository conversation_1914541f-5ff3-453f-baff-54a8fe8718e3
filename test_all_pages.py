#!/usr/bin/env python3
"""
Comprehensive System Test - All Pages

This script tests every page and functionality in the system to ensure
everything is working before going live.
"""

import os
import sys
import requests
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://127.0.0.1:5000"

def test_page(url, page_name):
    """Test if a page loads successfully"""
    try:
        response = requests.get(f"{BASE_URL}{url}")
        if response.status_code == 200:
            print(f"   ✅ {page_name}: PASS")
            return True
        else:
            print(f"   ❌ {page_name}: FAIL (HTTP {response.status_code})")
            return False
    except Exception as e:
        print(f"   ❌ {page_name}: FAIL (Error: {str(e)})")
        return False

def test_api_endpoint(url, endpoint_name, method='GET', data=None):
    """Test if an API endpoint works"""
    try:
        if method == 'GET':
            response = requests.get(f"{BASE_URL}{url}")
        elif method == 'POST':
            response = requests.post(f"{BASE_URL}{url}", json=data, headers={'Content-Type': 'application/json'})
        
        if response.status_code in [200, 201]:
            result = response.json()
            if result.get('success', True):  # Some APIs don't have success field
                print(f"   ✅ {endpoint_name}: PASS")
                return True
            else:
                print(f"   ❌ {endpoint_name}: FAIL (API Error: {result.get('error', 'Unknown')})")
                return False
        else:
            print(f"   ❌ {endpoint_name}: FAIL (HTTP {response.status_code})")
            return False
    except Exception as e:
        print(f"   ❌ {endpoint_name}: FAIL (Error: {str(e)})")
        return False

def main():
    """Run comprehensive system test"""
    print("🚀 COMPREHENSIVE SYSTEM TEST - ALL PAGES")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            print("❌ Flask server not running at http://127.0.0.1:5000")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Flask server at http://127.0.0.1:5000")
        return False
    
    print("✅ Flask server is running\n")
    
    # Test results storage
    results = []
    
    # 📊 CORE WORKFLOW PAGES
    print("📊 TESTING CORE WORKFLOW PAGES:")
    results.append(("Dashboard", test_page("/", "Dashboard")))
    results.append(("Migration Orchestrator", test_page("/migration_orchestrator", "Migration Orchestrator")))
    results.append(("Migration Jobs", test_page("/migration_jobs", "Migration Jobs")))
    
    # 🔧 MIGRATION TOOLS PAGES
    print("\n🔧 TESTING MIGRATION TOOLS PAGES:")
    results.append(("Upload Modules", test_page("/upload_modules", "Upload Modules")))
    results.append(("GitHub Integration", test_page("/github_integration", "GitHub Integration")))
    results.append(("Analyze Modules", test_page("/analyze_modules", "Analyze Modules")))
    results.append(("Bulk Migration", test_page("/bulk_migration", "Bulk Migration")))
    results.append(("Automation Dashboard", test_page("/automation", "Automation Dashboard")))
    results.append(("Contribute Modules", test_page("/contributor_upload", "Contribute Modules")))
    
    # 🧪 TESTING & ENVIRONMENTS PAGES
    print("\n🧪 TESTING & ENVIRONMENTS PAGES:")
    results.append(("Testing Dashboard", test_page("/testing_dashboard", "Testing Dashboard")))
    results.append(("Manual Interventions", test_page("/manual_interventions", "Manual Interventions")))
    results.append(("Docker Environments", test_page("/docker_environments", "Docker Environments")))
    
    # ⚙️ SYSTEM & CONFIGURATION PAGES
    print("\n⚙️ TESTING SYSTEM & CONFIGURATION PAGES:")
    results.append(("Health Monitor", test_page("/health_dashboard", "Health Monitor")))
    results.append(("AI Settings", test_page("/ai_providers", "AI Settings")))
    
    # 🔌 API ENDPOINTS
    print("\n🔌 TESTING CRITICAL API ENDPOINTS:")
    results.append(("Migration Jobs API", test_api_endpoint("/api/migration-jobs", "Migration Jobs API")))
    results.append(("GitHub Auth API", test_api_endpoint("/api/github/auth-status", "GitHub Auth API")))
    results.append(("GitHub Repos API", test_api_endpoint("/api/github/repositories", "GitHub Repos API")))
    results.append(("Intervention Queue API", test_api_endpoint("/api/intervention-queue", "Intervention Queue API")))
    results.append(("Docker Environments API", test_api_endpoint("/api/docker-environments", "Docker Environments API")))
    
    # 📝 FUNCTIONAL TESTS
    print("\n📝 TESTING FUNCTIONAL WORKFLOWS:")
    
    # Test GitHub module pulling
    github_pull_result = test_api_endpoint(
        "/api/github/pull-modules", 
        "GitHub Module Pull", 
        method='POST',
        data={"repository_url": "https://github.com/test/functional-test"}
    )
    results.append(("GitHub Module Pull", github_pull_result))
    
    # Test job status if we have jobs
    job_status_result = test_api_endpoint("/api/job-status/1", "Job Status API")
    results.append(("Job Status API", job_status_result))
    
    # Test visual diff
    visual_diff_result = test_api_endpoint("/api/visual-diff/1", "Visual Diff API")
    results.append(("Visual Diff API", visual_diff_result))
    
    # SUMMARY
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST RESULTS:")
    print("=" * 60)
    
    # Group results by category
    core_results = results[0:3]
    tools_results = results[3:9]
    testing_results = results[9:12]
    system_results = results[12:14]
    api_results = results[14:19]
    functional_results = results[19:]
    
    categories = [
        ("📊 CORE WORKFLOW", core_results),
        ("🔧 MIGRATION TOOLS", tools_results),
        ("🧪 TESTING & ENVIRONMENTS", testing_results),
        ("⚙️ SYSTEM & CONFIGURATION", system_results),
        ("🔌 API ENDPOINTS", api_results),
        ("📝 FUNCTIONAL WORKFLOWS", functional_results)
    ]
    
    total_passed = 0
    total_tests = 0
    
    for category_name, category_results in categories:
        passed = sum(1 for _, result in category_results if result)
        total = len(category_results)
        total_passed += passed
        total_tests += total
        
        print(f"\n{category_name}: {passed}/{total} PASSED")
        for test_name, result in category_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {test_name}: {status}")
    
    print(f"\n🎯 OVERALL SYSTEM STATUS: {total_passed}/{total_tests} TESTS PASSED")
    
    if total_passed == total_tests:
        print("🎉 ALL TESTS PASSED! SYSTEM IS READY TO GO LIVE! 🚀")
    else:
        failed_tests = total_tests - total_passed
        print(f"⚠️  {failed_tests} TESTS FAILED. ISSUES NEED TO BE FIXED BEFORE GOING LIVE.")
        
        print("\n🔧 FAILED TESTS THAT NEED FIXING:")
        for category_name, category_results in categories:
            failed_in_category = [(name, result) for name, result in category_results if not result]
            if failed_in_category:
                print(f"\n{category_name}:")
                for test_name, _ in failed_in_category:
                    print(f"   ❌ {test_name}")
    
    return total_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
