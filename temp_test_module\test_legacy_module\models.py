# -*- coding: utf-8 -*-
from odoo import models, fields, api
from odoo.exceptions import ValidationError

class TestLegacyModel(models.Model):
    """Test model with legacy patterns that need upgrading"""
    _name = 'test.legacy.model'
    _description = 'Test Legacy Model'
    
    name = fields.Char('Name', required=True)
    description = fields.Text('Description')
    active = fields.Boolean('Active', default=True)
    sequence = fields.Integer('Sequence', default=10)
    
    # Legacy @api.one decorator (should be removed)
    @api.one
    def legacy_method_one(self):
        """Legacy method with @api.one decorator"""
        self.name = self.name.upper()
        return True
    
    # Legacy @api.multi decorator (should be removed)
    @api.multi
    def legacy_method_multi(self):
        """Legacy method with @api.multi decorator"""
        for record in self:
            record.sequence += 1
        return True
    
    # Legacy @api.one with @api.depends (needs complex transformation)
    @api.one
    @api.depends('name')
    def _compute_display_name(self):
        """Legacy computed field"""
        self.display_name = f"[{self.sequence}] {self.name}"
    
    display_name = fields.Char(compute='_compute_display_name', store=True)
    
    # Legacy constraints
    @api.constrains('sequence')
    def _check_sequence(self):
        """Check sequence value"""
        if any(record.sequence < 0 for record in self):
            raise ValidationError('Sequence must be positive')
    
    # Legacy onchange method
    @api.onchange('name')
    def _onchange_name(self):
        """Legacy onchange method"""
        if self.name:
            self.description = f"Description for {self.name}"

class TestLegacyWizard(models.TransientModel):
    """Test wizard with legacy patterns"""
    _name = 'test.legacy.wizard'
    _description = 'Test Legacy Wizard'
    
    name = fields.Char('Name', required=True)
    model_ids = fields.Many2many('test.legacy.model', string='Models')
    
    @api.multi
    def action_process(self):
        """Legacy wizard action"""
        for wizard in self:
            wizard.model_ids.write({'active': False})
        return {'type': 'ir.actions.act_window_close'}