#!/usr/bin/env python3
"""
Create Demo Migration Jobs for Testing

This script creates some completed migration jobs so we can test the 
GitHub sync back functionality.
"""

import os
import sys
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from models import OdooModule, MigrationJob

def create_demo_migrations():
    """Create demo migration jobs for testing"""
    app = create_app()
    
    with app.app_context():
        print("🔧 Creating demo migration jobs...")
        
        # Get existing modules
        modules = OdooModule.query.all()
        
        if not modules:
            print("❌ No modules found. Please upload or pull some modules first.")
            return False
        
        created_jobs = 0
        
        for module in modules[:3]:  # Create jobs for first 3 modules
            # Check if migration job already exists
            existing_job = MigrationJob.query.filter_by(module_id=module.id).first()
            
            if existing_job:
                # Update existing job to COMPLETED status
                existing_job.status = 'COMPLETED'
                existing_job.target_version = '18.0'
                existing_job.timestamp = datetime.now()
                print(f"   ✅ Updated migration job for {module.name}")
            else:
                # Create new completed migration job
                migration_job = MigrationJob(
                    module_id=module.id,
                    target_version='18.0',
                    status='COMPLETED',
                    timestamp=datetime.now(),
                    log=f"Demo migration completed for {module.name}"
                )
                db.session.add(migration_job)
                print(f"   ✅ Created migration job for {module.name}")
            
            created_jobs += 1
        
        # Commit changes
        db.session.commit()
        
        print(f"\n🎉 Successfully created/updated {created_jobs} migration jobs")
        print("   These can now be used to test GitHub sync back functionality")
        
        return True

if __name__ == "__main__":
    success = create_demo_migrations()
    sys.exit(0 if success else 1)
