[options]
; This is the password that allows database operations:
; WARNING: change this password for production use!
admin_passwd = admin123

; Database connection settings
db_host = localhost
db_port = 5432
db_user = postgres
db_password = 
db_name = False
db_template = template0
db_maxconn = 64
db_sslmode = prefer

; Server settings
xmlrpc = True
xmlrpc_interface = 0.0.0.0
xmlrpc_port = 8069
netrpc = False
longpolling_port = 8072

; Web interface settings
proxy_mode = False
without_demo = False

; Logging settings
log_level = info
logfile = /opt/odoo18/odoo.log
log_db = False
log_db_level = warning
log_handler = :INFO
syslog = False

; Performance settings
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 600
limit_time_real = 1200
limit_time_real_cron = -1
max_cron_threads = 2
workers = 0

; Addons settings
addons_path = /opt/odoo18/addons,/opt/odoo18/odoo/addons,/opt/odoo18/odoo/odoo/addons
upgrade_path = 

; Data directory
data_dir = /opt/odoo18/data

; Security settings
list_db = True
admin_passwd = admin123

; Email settings (configure for production)
email_from = False
smtp_server = localhost
smtp_port = 25
smtp_ssl = False
smtp_user = False
smtp_password = False

; Internationalization
translate_modules = ['all']
language = en_US

; Developer mode (disable for production)
dev_mode = False

; CSV import settings
csv_internal_sep = ,

; Demo data
without_demo = all

; Multiprocessing settings (for production with multiple workers)
; workers = 4
; max_cron_threads = 1

; SSL/TLS settings (for HTTPS)
; secure_cert_file = /path/to/cert.pem
; secure_pkey_file = /path/to/private.key

; Session settings
; session_dir = /tmp

; Geolocation settings
; geoip_database = /usr/share/GeoIP/GeoLiteCity.dat

; Test settings
test_enable = False
test_file = False
test_tags = None

; Update settings
update = None
init = None

; Stop after init
stop_after_init = False

; Shell interface
shell_interface = 

; Screencasts
screencasts = 

; Screenshots
screenshots = /tmp/odoo_tests

; Server-wide modules
server_wide_modules = base,web

; Load demonstration data
demo = {}

; Unaccent
unaccent = False

; PG path (PostgreSQL executables path)
pg_path = 

; HTTP settings
http_enable = True
http_interface = 0.0.0.0
http_port = 8069

; Gevent settings (for better concurrency)
; gevent_port = 8069

; WSGI settings
; wsgi_enable = False

; Database options
; dbfilter = .*

; Load language
; load_language = en_US

; Timezone
; timezone = UTC

; Transient age (cleanup old transient data)
; osv_memory_age_limit = 1.0
; osv_memory_count_limit = False

; Reportgz (compress reports)
; reportgz = False

; X-Sendfile (for nginx)
; x_sendfile = False

; Static http settings
; static_http_enable = True
; static_http_document_root = /var/lib/odoo/static/
; static_http_url_prefix = /static/

; Additional options for advanced configurations
; These should be uncommented and configured based on your needs

; Multi-database setup
; dbfilter = ^%d$

; LDAP Authentication
; ldap_server = ldap.example.com
; ldap_server_port = 389
; ldap_binddn = cn=admin,dc=example,dc=com
; ldap_password = admin_password
; ldap_filter = (uid=%s)
; ldap_base = ou=users,dc=example,dc=com

; Custom addons repositories
; You can add multiple addon paths separated by commas
; addons_path = /opt/odoo18/addons,/opt/odoo18/custom-addons,/opt/odoo18/odoo/addons

; Cron settings
; max_cron_threads = 2

; Import/Export settings
; import_partial = 

; Security
; secure_pkey_file = 
; secure_cert_file = 

; Monitoring and profiling
; enable_profiler = False
